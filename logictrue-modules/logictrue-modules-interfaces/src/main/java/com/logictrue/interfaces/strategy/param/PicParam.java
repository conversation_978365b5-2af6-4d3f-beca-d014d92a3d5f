package com.logictrue.interfaces.strategy.param;


import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

public class PicParam extends ParamStrategy {

    private static final Logger logger = LoggerFactory.getLogger(PicParam.class);

    private JSONObject dbObject;

    public PicParam(JSONObject dbObject) {
        this.dbObject = dbObject;
    }

    @Override
    public String format(String param, String pattern) {

        String[] pics = pattern.split("#");
        String[] position = pics[1].split("-");
        int row1 = Integer.parseInt(position[0]);
        int column1 = Integer.parseInt(position[1]);
        int row2 = Integer.parseInt(position[2]);
        int column2 = Integer.parseInt(position[3]);

        // Debug: 打印设计时配置的位置信息
        logger.debug("图片设计时配置位置 - 起始行: {}, 起始列: {}, 结束行: {}, 结束列: {}",
                    row1, column1, row2, column2);

        //存在动态行数,先检验图片是否在动态行下方
        Map<Integer, Integer> colMap = (Map<Integer, Integer>) dbObject.get("colAddCntMap");

        int colNum = column2 - column1;

        boolean shouldDynamic = false;

        Map<Integer, Integer> picMap = new HashMap<>();

        for (int i = 0; i < colNum; i++) {
            if (colMap.containsKey(column1 + i)){
                shouldDynamic = true;
                picMap.put(column1 + i, colMap.get(column1 + i));
            }
        }

        if (shouldDynamic) {
            int max = 0;
            for (Integer value : picMap.values()) {
                if (value > max) {
                    max = value;
                }
            }
            logger.debug("检测到动态行数，最大偏移量: {}", max);
            logger.debug("动态调整前位置 - 起始行: {}, 结束行: {}", row1, row2);
            row1 += max;
            row2 += max;
            logger.debug("动态调整后位置 - 起始行: {}, 结束行: {}", row1, row2);
        }

        // 记录图片的行列坐标信息，不再直接计算像素位置
        recordImageCoordinates(param, row1, column1, row2, column2);

        return "";
    }

    /**
     * 记录图片的行列坐标信息
     * 在单元格渲染过程中只记录坐标，不计算像素位置
     */
    private void recordImageCoordinates(String imageSrc, int row1, int column1, int row2, int column2) {
        // 获取或创建图片坐标存储对象
        JSONObject imageCoordinates = dbObject.getJSONObject("imageCoordinates");
        if (imageCoordinates == null) {
            imageCoordinates = new JSONObject();
            dbObject.put("imageCoordinates", imageCoordinates);
        }

        String imageName = "img_" + getRandomString() + "_" + System.currentTimeMillis();

        // 创建图片坐标信息
        JSONObject imageInfo = new JSONObject();
        imageInfo.put("src", imageSrc);
        imageInfo.put("row1", row1);
        imageInfo.put("column1", column1);
        imageInfo.put("row2", row2);
        imageInfo.put("column2", column2);

        // 存储图片坐标信息
        imageCoordinates.put(imageName, imageInfo);

        logger.debug("记录图片坐标信息 - 图片名称: {}, 行列范围: [{}-{}, {}-{}]",
                    imageName, row1, row2, column1, column2);
    }

    /**
     * 计算图片的像素位置
     * 根据行列坐标和当前的行高列宽配置计算像素位置
     *
     * @param row1 起始行
     * @param column1 起始列
     * @param row2 结束行
     * @param column2 结束列
     * @param rowlen 行高配置
     * @param columnlen 列宽配置
     * @return 包含left、top、width、height的数组
     */
    public static int[] calculateImagePixelPosition(int row1, int column1, int row2, int column2,
                                                   JSONObject rowlen, JSONObject columnlen) {
        int defColumnLen = 73;
        int defRowLen = 19;

        // 计算宽度和左边距
        int width = 0;
        int left = 0;
        for (int i = 0; i < column2; i++) {
            int c = defColumnLen;
            if (columnlen != null && columnlen.containsKey(String.valueOf(i))) {
                Integer colLen = columnlen.getInteger(String.valueOf(i));
                if (colLen != null) {
                    c = colLen;
                }
            }
            if (i < column1) {
                left += c;
            }
            if (i >= column1) {
                width += c;
            }
        }

        // 计算高度和顶部距离
        int height = 0;
        int top = 0;

        // 计算顶部距离：累加从第0行到row1-1行的高度
        for (int i = 0; i < row1; i++) {
            int r = defRowLen;
            if (rowlen != null && rowlen.containsKey(String.valueOf(i))) {
                Integer rowLen = rowlen.getInteger(String.valueOf(i));
                if (rowLen != null) {
                    r = rowLen;
                }
            }
            top += r;
        }

        // 计算图片高度：累加从row1行到row2-1行的高度
        for (int i = row1; i < row2; i++) {
            int r = defRowLen;
            if (rowlen != null && rowlen.containsKey(String.valueOf(i))) {
                Integer rowLen = rowlen.getInteger(String.valueOf(i));
                if (rowLen != null) {
                    r = rowLen;
                }
            }
            height += r;
        }

        return new int[]{left, top, width, height};
    }

    /**
     * 处理所有图片的位置计算
     * 在自动行高计算完成后调用，使用最新的行高配置计算图片位置
     *
     * @param dbObject 数据对象
     */
    public static void processAllImagePositions(JSONObject dbObject) {
        JSONObject imageCoordinates = dbObject.getJSONObject("imageCoordinates");
        if (imageCoordinates == null || imageCoordinates.isEmpty()) {
            return;
        }

        JSONObject config = dbObject.getJSONObject("config");
        JSONObject rowlen = config != null ? config.getJSONObject("rowlen") : null;
        JSONObject columnlen = config != null ? config.getJSONObject("columnlen") : null;

        // 获取或创建images对象
        JSONObject images = dbObject.getJSONObject("images");
        if (images == null) {
            images = new JSONObject();
            dbObject.put("images", images);
        }

        logger.debug("开始处理所有图片位置计算，共{}张图片", imageCoordinates.size());

        JSONObject finalImages = images;
        imageCoordinates.forEach((imageName, imageInfoObj) -> {
            JSONObject imageInfo = (JSONObject) imageInfoObj;

            String src = imageInfo.getString("src");
            int row1 = imageInfo.getInteger("row1");
            int column1 = imageInfo.getInteger("column1");
            int row2 = imageInfo.getInteger("row2");
            int column2 = imageInfo.getInteger("column2");

            // 计算像素位置
            int[] position = calculateImagePixelPosition(row1, column1, row2, column2, rowlen, columnlen);
            int left = position[0];
            int top = position[1];
            int width = position[2];
            int height = position[3];

            logger.debug("图片{}位置计算 - 行列范围: [{}-{}, {}-{}], 像素位置: [left={}, top={}, width={}, height={}]",
                        imageName, row1, row2, column1, column2, left, top, width, height);

            // 创建图片配置
            JSONObject image = new JSONObject();
            image.put("type", "3");
            image.put("src", src);

            JSONObject def = new JSONObject();
            def.put("width", width);
            def.put("height", height);
            def.put("left", left);
            def.put("top", top);

            JSONObject crop = new JSONObject();
            crop.put("width", width);
            crop.put("height", height);
            crop.put("offsetLeft", 0);
            crop.put("offsetTop", 0);

            JSONObject border = new JSONObject();
            border.put("width", 0);
            border.put("radius", 0);
            border.put("style", "solid");
            border.put("color", "#000");

            image.put("default", def);
            image.put("crop", crop);
            image.put("border", border);

            finalImages.put(imageName, image);

        });

        // 清理临时的坐标信息
        dbObject.remove("imageCoordinates");

    }

    private String getRandomString() {
        String[] code = {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z"};
        int len = 12;
        StringBuilder randomStr = new StringBuilder();
        for (int i = 0; i < len; i++) {
            int random = new Random().nextInt(code.length);
            randomStr.append(code[random]);
        }
        return randomStr.toString();
    }
}
