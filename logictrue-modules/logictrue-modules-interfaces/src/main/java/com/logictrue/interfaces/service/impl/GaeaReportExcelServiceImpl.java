/*
 * Copyright (c) 2021 伦图科技（长沙）有限公司. All rights reserved.
 */

package com.logictrue.interfaces.service.impl;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.aspose.cells.*;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.logictrue.common.core.constant.MongoConstants;
import com.logictrue.common.core.constant.ParamConstants;
import com.logictrue.common.core.text.UUID;
import com.logictrue.common.core.utils.StringUtils;
import com.logictrue.common.core.utils.file.FileUtils;
import com.logictrue.common.datasource.mybatis.Wraps;
import com.logictrue.common.dozer.utils.DozerUtil;
import com.logictrue.common.security.service.TokenService;
import com.logictrue.interfaces.LdfReportConfig;
import com.logictrue.interfaces.domain.ExecuteEntity;
import com.logictrue.interfaces.domain.GaeaReportExcel;
import com.logictrue.interfaces.domain.MultiPageCalculate;
import com.logictrue.interfaces.domain.dto.DataSetDto;
import com.logictrue.interfaces.domain.dto.OriginalDataDto;
import com.logictrue.interfaces.domain.dto.ReportExcelDto;
import com.logictrue.interfaces.domain.dto.ReportExcelMoreDto;
import com.logictrue.interfaces.domain.vo.Excel2PdfParamVo;
import com.logictrue.interfaces.domain.vo.PreviewVO;
import com.logictrue.interfaces.entity.po.LdfReportInfo;
import com.logictrue.interfaces.interfaces.core.service.MagicAPIService;
import com.logictrue.interfaces.interfaces.core.servlet.MagicHttpServletRequest;
import com.logictrue.interfaces.interfaces.core.servlet.MagicHttpServletResponse;
import com.logictrue.interfaces.interfaces.utils.WebUtils;
import com.logictrue.interfaces.mapper.GaeaReportExcelMapper;
import com.logictrue.interfaces.service.*;
import com.logictrue.interfaces.service.excel.AnalysisSheetService;
import com.logictrue.interfaces.strategy.sheet.SheetContext;
import com.logictrue.interfaces.utils.XlsUtil;
import com.logictrue.system.api.model.LoginUser;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.ssssssss.script.runtime.ExitValue;
import sun.security.action.GetPropertyAction;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.security.AccessController;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-06-30
 */
@Log4j2
@Service(value = "gaeaReportExcelService")
public class GaeaReportExcelServiceImpl extends ServiceImpl<GaeaReportExcelMapper, GaeaReportExcel> implements IGaeaReportExcelService {
    // 创建一个线程池
    private static final ThreadPoolExecutor threadPool = new ThreadPoolExecutor(8, 12, 30,

            TimeUnit.SECONDS, new ArrayBlockingQueue<Runnable>(5000), new ThreadPoolExecutor.AbortPolicy());
    @Autowired
    private DozerUtil dozerUtil;
    @Autowired
    private IGaeaReportService iGaeaReportService;
    @Autowired
    private IMagicApiFileService iMagicApiFileService;
    @Autowired
    private MagicAPIService magicAPIService;
    @Autowired
    private HttpServletResponse httpServletResponse;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private GaeaReportExcelPageHandler pageHandler;
    @Autowired
    private AnalysisSheetService analysisSheetService;
    @Autowired
    private GenOperationUtils genOperationUtils;
    @Autowired
    private LdfReportInfoService ldfReportInfoService;
    @Autowired
    private LdfInterfacesInfoService ldfInterfacesInfoService;
    @Autowired
    private XlsUtil xlsUtil;
    @Autowired
    private LdfReportConfig ldfReportConfig;

    /**
     * 获取当前树最高层级
     */
    public static Integer getTreeHeight(JSONObject tree, Integer max) {
        if (tree == null || !tree.containsKey("child") || tree.getJSONArray("child").size() == 0) {
            return max;
        }
        List<Integer> heights = new ArrayList<>();
        JSONArray child = tree.getJSONArray("child");
        for (int i = 0; i < child.size(); i++) {
            heights.add(getTreeHeight((JSONObject) child.get(i), 1));
        }
        return heights.stream().max((x, y) -> x - y).get() + max;
    }

    /**
     * 获取当前树最宽宽度
     */
    public static Integer getTreeWidth(JSONObject tree) {
        if (tree == null || !tree.containsKey("child") || tree.getJSONArray("child").size() == 0) {
            return 1;
        }
        List<Integer> wit = new ArrayList<>();
        JSONArray child = tree.getJSONArray("child");
        for (int i = 0; i < child.size(); i++) {
            wit.add(getTreeWidth((JSONObject) child.get(i)));
        }
        Integer reduce = wit.stream().reduce(0, (x, y) -> x + y);

        return reduce;
    }

    /**
     * 获取当前树最宽宽度
     */
    public static Integer getTreeMaxHeight(Integer level, List<JSONObject> cellDynamicData) {
        if (cellDynamicData == null || cellDynamicData.size() == 0) {
            return level;
        }
        List<Integer> wit = new ArrayList<>();
        for (int i = 0; i < cellDynamicData.size(); i++) {
            JSONObject cellDynamicDatum = JSON.parseObject(JSON.toJSONString(cellDynamicData.get(i)));
            if (cellDynamicDatum.containsKey("child")) {
                JSONArray child = cellDynamicDatum.getJSONArray("child");
                wit.add(getTreeMaxHeight(1, child.toJavaList(JSONObject.class)));
            } else {
                wit.add(0);
            }
        }
        Integer reduce = wit.stream().max((x, y) -> x - y).get();
        return level + reduce;
    }

    /**
     *
     */
    public static Integer getLevelHeight(Integer level, List<JSONObject> cellDynamicData) {
        if (level == 0) {
            JSONArray jsonArray = JSON.parseArray(JSON.toJSONString(cellDynamicData));
            return jsonArray.stream().map(json -> getTreeHeight(JSON.parseObject(JSON.toJSONString(json)), 1)).max((x, y) -> x - y).get();
        }
        return getLevelHeight(level - 1, getChildObject(cellDynamicData));
    }

    public static List<JSONObject> getChildObject(List<JSONObject> cellDynamicData) {
        List<JSONObject> jsonObjects = new ArrayList<>();
        JSONArray jsonArray = JSON.parseArray(JSON.toJSONString(cellDynamicData));
        jsonArray.stream().forEach(e -> {
            JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(e));
            if (jsonObject.containsKey("child")) {
                jsonObjects.addAll(jsonObject.getJSONArray("child").toJavaList(JSONObject.class));
            }
        });
        return jsonObjects;
    }

    /**
     * 获取 license 去除水印
     * 若不验证则转化出的pdf文档会有水印产生
     */
    private static void getLicense() {
        String licenseFilePath = "excel-license.xml";
        try {
            Resource resource = new ClassPathResource(licenseFilePath);
            InputStream is = resource.getInputStream();
            License license = new License();
            license.setLicense(is);
        } catch (Exception e) {
            System.out.println("license verify failed");
            e.printStackTrace();
        }
    }

    /**
     * 根据报表编码查询详情
     *
     * @param reportCode
     * @return
     */
    @Override
    public ReportExcelDto detailByReportCode(String reportCode) {
        GaeaReportExcel one = this.getOne(Wraps.<GaeaReportExcel>lbQ().eq(GaeaReportExcel::getReportCode, reportCode), Boolean.FALSE);
        return dozerUtil.map(one, ReportExcelDto.class);
    }

    /**
     * 报表预览
     *
     * @param reportExcelDto
     * @return
     */
    @Override
    public ReportExcelDto preview(ReportExcelDto reportExcelDto) {
        //兼容reportCode,reportCode将逐步弃用
        String reportUid = StringUtils.isNotBlank(reportExcelDto.getReportUid()) ? reportExcelDto.getReportUid() : reportExcelDto.getReportCode();
        LdfReportInfo reportInfo = getLdfReportInfoByReportUid(reportUid);
        //GaeaReportExcel reportExcel = this.getOne(reportCode, Wraps.<GaeaReportExcel>lbQ().eq(GaeaReportExcel::getReportCode, reportCode), Boolean.FALSE);
        //GaeaReport report = iGaeaReportService.getOne(Wraps.<GaeaReport>lbQ().eq(GaeaReport::getReportCode, reportCode));
        String reportParam = reportExcelDto.getReportParam();//前段的传参
        String dbReportParam = reportInfo.getReportParam();//数据库保存的数据
        dozerUtil.map(reportInfo, reportExcelDto);
        if (StringUtils.isNotBlank(reportParam) &&
                !"{}".equals(reportParam)) {
            //进行追加
            //JSONObject db = JSON.parseObject(dbSetParam);
            //JSONObject set = JSON.parseObject(setParam);
            /*JSONArray dbArr = db.getJSONArray("common");
            JSONArray setCommon = set.getJSONArray("common");
            if (dbArr != null) {
                setCommon.addAll(dbArr);
            }*/
            //analyseSetParam(setParam, dbSetParam);
            reportExcelDto.setReportParam(reportParam);
        }

        reportExcelDto.setReportName(reportInfo.getReportName());
        // 数据集解析
        String jsonStr = analysisReportData(reportExcelDto);
        reportExcelDto.setReportParam(dbReportParam);
        reportExcelDto.setReportContent(jsonStr);
//        reportExcelDto.setTotal(jsonObject.getJSONObject("rows").size());
        // 解析接口返回的错误信息
        Map<String, String> errMsgMap = reportExcelDto.getErrMsgMap();
        if (!errMsgMap.isEmpty()) {
            reportExcelDto.setHasErrorMessage(true);
            List<String> values = new ArrayList<>(errMsgMap.values());
            StringBuilder msg = new StringBuilder();
            for (int i = 0; i < values.size(); i++) {
                if (i == 0) {
                    msg.append(values.get(i));
                } else {
                    msg.append("<br>").append(values.get(i));
                }
            }
            reportExcelDto.setErrorMessage(msg.toString());
        }
        return reportExcelDto;
    }

    private LdfReportInfo getLdfReportInfoByReportUid(String reportUid) {
        return ldfReportConfig.get(reportUid);
    }

    private void analyseSetParam(String setParam, String dbSetParam) {
        JSONObject db = JSON.parseObject(dbSetParam);
        JSONObject set = JSON.parseObject(setParam);
        set.keySet().forEach(s -> {
            JSONArray sJson = set.getJSONArray(s);
            for (int i = 0; i < sJson.size(); i++) {
                JSONObject property = sJson.getJSONObject(i);

            }
        });
    }


    /**
     * 导出为excel
     *
     * @param reportExcelDto
     * @return
     */
    @Override
    public void exportExcel(ReportExcelDto reportExcelDto) {
        String reportUid = reportExcelDto.getReportUid();
        LdfReportInfo reportInfo = getLdfReportInfoByReportUid(reportUid);
        String reportParam = reportExcelDto.getReportParam();
        if (StringUtils.isNotBlank(reportParam) &&
                !"{}".equals(reportParam)) {
            reportExcelDto.setReportParam(reportInfo.getReportParam());
        }
        dozerUtil.map(reportInfo, reportExcelDto);

        reportExcelDto.setReportName(reportInfo.getReportName());


        String jsonStr = analysisReportData(reportExcelDto);
        List<JSONObject> lists = (List<JSONObject>) JSON.parse(jsonStr);

        // 计算并优化实际行数和列数
        optimizeSheetDimensions(lists);

        UUID uuid = UUID.fastUUID();
        OutputStream out = null;
        InputStream fis = null;
        File tempFile = null;
        try {
            tempFile = File.createTempFile(uuid.toString(), "." + "xlsx");
            out = new FileOutputStream(tempFile);
            xlsUtil.exportXlsFile(out, true, lists);
            // 输出文件
            FileInputStream fileInputStream = new FileInputStream(tempFile);
            fis = new BufferedInputStream(fileInputStream);
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);


            // 清空response
            httpServletResponse.reset();
            // 设置response的Header
            httpServletResponse.setCharacterEncoding("UTF-8");
            // filename表示文件的默认名称，因为网络传输只支持URL编码的相关支付，因此需要将文件名URL编码后进行传输,前端收到后需要反编码才能获取到真正的名称
            httpServletResponse.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("报表导出.xlsx", "UTF-8"));
            httpServletResponse.addHeader("Content-Length", "" + tempFile.length());
            OutputStream outputStream = new BufferedOutputStream(httpServletResponse.getOutputStream());
            httpServletResponse.setContentType("application/octet-stream");
            outputStream.write(buffer);
            outputStream.flush();
        } catch (IOException e) {
            log.error("导出失败", e);
        } finally {
            // 删除临时文件
            FileUtils.deleteFile(tempFile.getPath());
            try {
                // 关闭流
                out.close();
                fis.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }


    /**
     * 导出为pdf
     *
     * @param reportExcelDto
     * @return
     */
    @Override
    public void exportPdf(ReportExcelDto reportExcelDto) {
        export(reportExcelDto, SaveFormat.PDF, "pdf");
    }

    /**
     * 多个报表导出为pdf
     *
     * @param reportExcelDto
     * @return
     */
    @Override
    public void exportPdfList(ReportExcelMoreDto reportExcelDto) {
        if (CollectionUtils.isEmpty(reportExcelDto.getParam())) {
            throw new RuntimeException("参数有误");
        }
        for (Map<String, Object> map : reportExcelDto.getParam()) {
            //reportCode将逐步弃用
            if (!map.containsKey("reportCode") && !map.containsKey("reportUid")) {
                throw new RuntimeException("参数有误");
            }
        }
        exportList(reportExcelDto, SaveFormat.PDF, "pdf");
    }


    /**
     * 导出文件
     *
     * @param reportExcelDto
     * @return
     */
    @Override
    public void exportFile(ReportExcelDto reportExcelDto) {
        export(reportExcelDto, reportExcelDto.getSaveFormat(), reportExcelDto.getSuffix());
    }

    public <T> Map<String, T> json2map(String str_json) {
        Map<String, T> res = null;
        try {
            Gson gson = new Gson();
            res = gson.fromJson(str_json, new TypeToken<Map<String, T>>() {
            }.getType());
        } catch (JsonSyntaxException e) {
            log.error("json2map error");
        }
        return res;
    }

    @Override
    public void table2Pdf(Excel2PdfParamVo excel2PdfParamVo) {
        File tmpdir = new File(AccessController.doPrivileged(new GetPropertyAction("java.io.tmpdir")));
        String excelOutPath = tmpdir.getAbsolutePath() + File.separator + UUID.fastUUID() + ".xlsx";
        // 创建Excel
        if (Boolean.FALSE.equals(excel2PdfParamVo.isV2())) {
            creatExcelByApiId(excel2PdfParamVo, excelOutPath);
        } else {
            creatExcelByTableName(excel2PdfParamVo, excelOutPath);
        }
        FileOutputStream fileOS = null;
        File tempPdfFile = null;

        try {
            // 假设仅仅为输出Excel 则直接返回携带数据的Excel文件流
            if (Boolean.TRUE.equals(excel2PdfParamVo.isOutExcel())) {
                this.outFile(FileUtil.file(excelOutPath));
            } else {

                ReportExcelDto reportExcelDto = excel2PdfParamVo.getReportExcelDto();
                tempPdfFile = new File(tmpdir, reportExcelDto.getUnicode() + ".pdf");
                fileOS = new FileOutputStream(tempPdfFile);
                // 验证 License 打印
                getLicense();
                getPrintingFile(reportExcelDto, excelOutPath, fileOS, SaveFormat.PDF);

                this.outFile(FileUtil.file(tempPdfFile));
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            // 删除临时文件
            if (null != tempPdfFile) {
                tempPdfFile.deleteOnExit();
            }
            FileUtils.deleteFile(FileUtil.getAbsolutePath(excelOutPath));
            try {
                if (null != fileOS) {
                    fileOS.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void report2Pdf(Excel2PdfParamVo excel2PdfParamVo) {
        ReportExcelDto reportExcelDto = excel2PdfParamVo.getReportExcelDto();
        Map<String, Object> body = excel2PdfParamVo.getBody();
        //组装参数
        JSONObject param = new JSONObject();
        JSONArray common = new JSONArray();
        param.put("common", common);
        if (body != null) {
            body.forEach((s, o) -> {
                JSONObject obj = new JSONObject();
                obj.put("name", s);
                obj.put("value", o);
                common.add(obj);
            });
        }
        reportExcelDto.setReportParam(param.toString());

        exportPdf(reportExcelDto);
    }

    private void creatExcelByTableName(Excel2PdfParamVo excel2PdfParamVo, String excelOutPath) {

        Page<Map<String, Object>> modelData = genOperationUtils.getModelData(excel2PdfParamVo.getBody(), excel2PdfParamVo.getTableName(), excel2PdfParamVo.getRequest(), excel2PdfParamVo.getResponse()).getData();
        List<Map<String, Object>> records = modelData.getRecords();

        List<Map<String, Object>> mapList = new ArrayList<>();
        if (null != records && records.size() > 0) {
            for (Map<String, Object> linkedHashMap : records) {
                Map<String, Object> map = new HashMap<>(linkedHashMap);
                mapList.add(map);
            }
        }

        xlsUtil.generateExcel(excel2PdfParamVo.getCols(), mapList, excelOutPath);
    }

    /**
     * 通过在线接口的信息 查询数据并按照 excel2PdfParamVo.cols 生成 Excel
     */
    private void creatExcelByApiId(Excel2PdfParamVo excel2PdfParamVo, String excelOutPath) {
        ExecuteEntity entity = new ExecuteEntity();
        entity.setApiId(excel2PdfParamVo.getApiId());
        entity.setBody(excel2PdfParamVo.getBody());
        Object execute = iMagicApiFileService.execute(entity);

        Map<String, Object> executeMap = json2map(JSON.toJSONString(execute));
        List<Map<String, Object>> records;

        // 假设为空  则非分页查询 直接转换成List
        if (null == executeMap) {
            records = JSON.parseObject(JSON.toJSONString(execute), new TypeReference<List<Map<String, Object>>>() {
            });
        } else {
            if (null != executeMap.get("records")) {
                records = JSON.parseObject(JSON.toJSONString(executeMap.get("records")), new TypeReference<List<Map<String, Object>>>() {
                });
            } else {  // 有可能只有一条数据 但是也没分页
                records = JSON.parseObject(JSON.toJSONString(execute), new TypeReference<List<Map<String, Object>>>() {
                });
            }
        }

        log.debug("在线接口查询返回： " + JSON.toJSONString(execute));
        xlsUtil.generateExcel(excel2PdfParamVo.getCols(), records, excelOutPath);
    }

    /**
     * 输出文件流
     */
    private void outFile(File file) throws IOException {
        InputStream fis = null;
        try {
            // 文件名
            String fileName = file.getName();
            // 输出文件
            FileInputStream fileInputStream = new FileInputStream(file);
            fis = new BufferedInputStream(fileInputStream);
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);

            // 清空response
            httpServletResponse.reset();
            // 设置response的Header
            httpServletResponse.setCharacterEncoding("UTF-8");
            // filename表示文件的默认名称，因为网络传输只支持URL编码的相关支付，因此需要将文件名URL编码后进行传输,前端收到后需要反编码才能获取到真正的名称
            httpServletResponse.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            httpServletResponse.addHeader("Content-Length", String.valueOf(file.length()));
            OutputStream outputStream = new BufferedOutputStream(httpServletResponse.getOutputStream());
            httpServletResponse.setContentType("application/octet-stream");
            outputStream.write(buffer);
            outputStream.flush();
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (fis != null) {
                fis.close();
            }
        }
    }

    private void exportList(ReportExcelMoreDto reportExcelDto, int saveFormat, String suffix) {
        String jsonStr = null;
        List<JSONObject> lists = null;
        for (Map<String, Object> r : reportExcelDto.getParam()) {
            //reportCode将逐步弃用
            if (r.containsKey("reportUid") || r.containsKey("reportCode")) {
                String reportUid = r.get("reportUid") != null ? r.get("reportUid").toString() :
                        r.get("reportCode") != null ? r.get("reportCode").toString() : "";
                LdfReportInfo report = ldfReportConfig.get(reportUid);
                if (report == null) {
                    throw new RuntimeException("报表不存在");
                }
                String setParam =  report.getReportParam();
                if (setParam != null && !"{}".equals(setParam)) {
                    JSONObject setParamJs = JSON.parseObject(setParam);
                    JSONArray commonArray = setParamJs.getJSONArray("common");
                    if (CollectionUtils.isNotEmpty(commonArray)) {
                        for (int i = 0; i < commonArray.size(); i++) {
                            JSONObject commonObject = commonArray.getJSONObject(i);
                            if (r.containsKey(commonObject.getString("name"))) {
                                commonObject.put("value", r.get(commonObject.getString("name")));
                            }
                        }
                    }
                    if (!report.getReportInterfacesIds().isEmpty()) {
                        String[] split = report.getReportInterfacesIds().split(",");
                        for (int i = 0; i < split.length; i++) {
                            JSONArray ApiArray = setParamJs.getJSONArray(split[i]);
                            if (CollectionUtils.isNotEmpty(ApiArray)) {
                                for (int j = 0; j < ApiArray.size(); j++) {
                                    JSONObject commonObject = ApiArray.getJSONObject(j);
                                    if (r.containsKey(commonObject.getString("name"))) {
                                        commonObject.put("value", r.get(commonObject.getString("name")));
                                    }
                                }
                            }
                        }
                    }
                    report.setReportParam(setParamJs.toJSONString());
                }
                dozerUtil.map(report, reportExcelDto.getReportExcelDto());
                jsonStr = analysisReportData(reportExcelDto.getReportExcelDto());
            }

            List<JSONObject> list = (List<JSONObject>) JSONObject.parse(jsonStr);
            if (lists == null) {
                lists = list;
            } else {
                lists.addAll(list);
            }
        }
        reportExcelDto.getReportExcelDto().setReportName("报表名称");
        lists.stream().forEach(jsonObject -> {
            boolean contains = jsonObject.getString("name") != null && jsonObject.getString("name").contains("#hx");
            if (contains) {//设置特定sheet名称的为横向打印
                jsonObject.put("name", "#hx" + UuidUtils.generateUuid());
            } else {
                jsonObject.put("name", UuidUtils.generateUuid());
            }

        });

        // 计算并优化实际行数和列数
        optimizeSheetDimensions(lists);

        String uuid = UUID.fastUUID().toString();
        OutputStream out = null;
        File tempExcelFile = null;
        File tempPdfFile = null;
        FileOutputStream fileOS = null;
        try {
            tempExcelFile = File.createTempFile(uuid, "." + "xlsx");
            File file = new File(AccessController.doPrivileged(new GetPropertyAction("java.io.tmpdir")));
            tempPdfFile = new File(file.getPath(), (StringUtils.isNotBlank(reportExcelDto.getUniCode()) ? reportExcelDto.getUniCode() : uuid) + "." + suffix);
            if (!tempPdfFile.exists()) {
                out = new FileOutputStream(tempExcelFile);
                XlsUtil.exportXlsFile(out, true, lists);
                fileOS = new FileOutputStream(tempPdfFile);

                // 验证 License 打印
                getLicense();
                getPrintingFile(reportExcelDto.getReportExcelDto(), tempExcelFile.getPath(), fileOS, saveFormat);
            }
        } catch (Exception e) {
            log.error("导出失败", e);
        } finally {
            if (tempExcelFile != null) FileUtils.deleteFile(tempExcelFile.getPath());
            if (tempPdfFile != null) tempPdfFile.deleteOnExit();
            try {
                if (out != null) out.close();
                if (fileOS != null) fileOS.close();
            } catch (IOException e) {
                log.error("关闭流失败" + e);
            }
        }
    }


    private void export(ReportExcelDto reportExcelDto, int saveFormat, String suffix) {
        String jsonStr = null;

        if (StringUtils.isNotBlank(reportExcelDto.getCustomExcel())) {
            jsonStr = reportExcelDto.getCustomExcel();
        } else {
            String reportUid = StringUtils.isNotBlank(reportExcelDto.getReportUid()) ? reportExcelDto.getReportUid() : reportExcelDto.getReportCode();
            LdfReportInfo reportInfo = getLdfReportInfoByReportUid(reportUid);
            String setParam = reportExcelDto.getReportParam();
            if (StringUtils.isNotBlank(setParam) &&
                    !"{}".equals(setParam)) {
                reportInfo.setReportParam(setParam);
            }
            dozerUtil.map(reportInfo, reportExcelDto);

            reportExcelDto.setReportName(reportInfo.getReportName());
            jsonStr = analysisReportData(reportExcelDto);
        }
        List<JSONObject> lists = (List<JSONObject>) JSON.parse(jsonStr);

        // 计算并优化实际行数和列数
        optimizeSheetDimensions(lists);
        UUID uuid = UUID.fastUUID();
        OutputStream out = null;
        InputStream fis = null;
        File tempExcelFile = null;
        File tempPdfFile = null;
        FileOutputStream fileOS = null;
        try {
            tempExcelFile = File.createTempFile(uuid.toString(), "." + "xlsx");
            File file = new File(AccessController.doPrivileged(new GetPropertyAction("java.io.tmpdir")));
            tempPdfFile = new File(file.getPath(), (StringUtils.isNotBlank(reportExcelDto.getUnicode()) ? reportExcelDto.getUnicode() : uuid.toString()) + "." + suffix);
            if (!tempPdfFile.exists()) {
                out = new FileOutputStream(tempExcelFile);
                XlsUtil.exportXlsFile(out, true, lists);
                fileOS = new FileOutputStream(tempPdfFile);

                // 验证 License 打印
                getLicense();
                getPrintingFile(reportExcelDto, tempExcelFile.getPath(), fileOS, saveFormat);
            }

            // 输出文件
            FileInputStream fileInputStream = new FileInputStream(tempPdfFile);
            fis = new BufferedInputStream(fileInputStream);
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);


            // 清空response
            httpServletResponse.reset();
            // 设置response的Header
            httpServletResponse.setCharacterEncoding("UTF-8");
            // filename表示文件的默认名称，因为网络传输只支持URL编码的相关支付，因此需要将文件名URL编码后进行传输,前端收到后需要反编码才能获取到真正的名称
            httpServletResponse.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("报表打印reportExcel." + suffix, "UTF-8"));
            httpServletResponse.addHeader("Content-Length", "" + tempPdfFile.length());
            OutputStream outputStream = new BufferedOutputStream(httpServletResponse.getOutputStream());
            httpServletResponse.setContentType("application/octet-stream");
            outputStream.write(buffer);
            outputStream.flush();
        } catch (Exception e) {
            log.error("导出失败", e);
        } finally {
            // 删除临时文件
            if (tempPdfFile != null) {
                tempPdfFile.deleteOnExit();
            } else {
                log.error("File对象为null，无法调用deleteOnExit。");
            }

            FileUtils.deleteFile(tempExcelFile.getPath());
            try {
                // 关闭流
                out.close();
                fis.close();
                fileOS.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 优化Sheet的行数和列数，根据实际数据计算
     * @param lists Sheet列表
     */
    private void optimizeSheetDimensions(List<JSONObject> lists) {
        if (lists == null || lists.isEmpty()) {
            log.debug("Sheet列表为空，跳过行列数优化");
            return;
        }

        for (int sheetIndex = 0; sheetIndex < lists.size(); sheetIndex++) {
            JSONObject sheet = lists.get(sheetIndex);
            String sheetName = sheet.getString("name");
            if (sheetName == null) {
                sheetName = "Sheet" + (sheetIndex + 1);
            }

            log.debug("开始优化Sheet[{}]的行列数", sheetName);

            // 获取原始行列数
            Integer originalRow = sheet.getInteger("row");
            Integer originalColumn = sheet.getInteger("column");
            log.debug("Sheet[{}]原始设置 - 行数: {}, 列数: {}", sheetName, originalRow, originalColumn);

            // 计算实际数据的最大行列数
            int maxDataRow = -1;
            int maxDataColumn = -1;

            if (sheet.containsKey("celldata") && sheet.get("celldata") != null) {
                List<JSONObject> celldata = (List<JSONObject>) sheet.get("celldata");
                log.debug("Sheet[{}]包含{}个单元格数据", sheetName, celldata.size());

                for (JSONObject cell : celldata) {
                    Integer r = cell.getInteger("r");
                    Integer c = cell.getInteger("c");

                    // 检查单元格是否有实际内容
                    JSONObject v = cell.getJSONObject("v");
                    if (v != null) {
                        String cellValue = v.getString("v");
                        if (StringUtils.isNotBlank(cellValue)) {
                            if (r != null && r > maxDataRow) {
                                maxDataRow = r;
                            }
                            if (c != null && c > maxDataColumn) {
                                maxDataColumn = c;
                            }
                            log.debug("Sheet[{}]单元格[{},{}]有数据: {}", sheetName, r, c,
                                    cellValue.length() > 50 ? cellValue.substring(0, 50) + "..." : cellValue);
                        }
                    }
                }
            }

            // 计算实际需要的行列数（索引+1）
            int actualRowCount = maxDataRow >= 0 ? maxDataRow + 1 : 1;
            int actualColumnCount = maxDataColumn >= 0 ? maxDataColumn + 1 : 1;

            log.debug("Sheet[{}]实际数据范围 - 最大行索引: {}, 最大列索引: {}", sheetName, maxDataRow, maxDataColumn);
            log.debug("Sheet[{}]计算得出的实际行列数 - 行数: {}, 列数: {}", sheetName, actualRowCount, actualColumnCount);

            // 更新Sheet的行列数为实际数据大小
            boolean updated = false;
            if (originalRow == null || originalRow > actualRowCount) {
                sheet.put("row", actualRowCount);
                updated = true;
                log.debug("Sheet[{}]行数已优化: {} -> {}", sheetName, originalRow, actualRowCount);
            }

            if (originalColumn == null || originalColumn > actualColumnCount) {
                sheet.put("column", actualColumnCount);
                updated = true;
                log.debug("Sheet[{}]列数已优化: {} -> {}", sheetName, originalColumn, actualColumnCount);
            }

            if (!updated) {
                log.debug("Sheet[{}]行列数无需优化", sheetName);
            }

            // 检查是否为空Sheet
            if (maxDataRow == -1 && maxDataColumn == -1) {
                log.warn("Sheet[{}]没有任何数据，可能会产生空白页", sheetName);
            }
        }

        log.debug("所有Sheet的行列数优化完成");
    }

    /**
     * 移除空白的worksheet以避免生成空白PDF页
     * @param wb Workbook对象
     */
    private void removeEmptyWorksheets(Workbook wb) {
        log.debug("开始检查并移除空白worksheet");

        // 从后往前遍历，避免删除时索引变化的问题
        for (int i = wb.getWorksheets().getCount() - 1; i >= 0; i--) {
            Worksheet worksheet = wb.getWorksheets().get(i);
            String sheetName = worksheet.getName();

            // 检查worksheet是否为空
            boolean isEmpty = isWorksheetEmpty(worksheet);

            if (isEmpty) {
                log.debug("发现空白worksheet[{}]，准备移除", sheetName);
                wb.getWorksheets().removeAt(i);
                log.debug("已移除空白worksheet[{}]", sheetName);
            } else {
                log.debug("worksheet[{}]包含数据，保留", sheetName);
            }
        }

        // 如果所有sheet都被移除了，至少保留一个空sheet避免错误
        if (wb.getWorksheets().getCount() == 0) {
            log.warn("所有worksheet都是空白的，创建一个默认worksheet");
            wb.getWorksheets().add("Sheet1");
        }

        log.debug("空白worksheet检查完成，剩余{}个worksheet", wb.getWorksheets().getCount());
    }

    /**
     * 检查worksheet是否为空
     * @param worksheet 要检查的worksheet
     * @return true表示为空，false表示有数据
     */
    private boolean isWorksheetEmpty(Worksheet worksheet) {
        try {
            // 获取数据范围
            int maxDataRow = worksheet.getCells().getMaxDataRow();
            int maxDataColumn = worksheet.getCells().getMaxDataColumn();

            log.debug("worksheet[{}]数据范围 - 最大行: {}, 最大列: {}",
                    worksheet.getName(), maxDataRow, maxDataColumn);

            // 如果没有数据行和列，认为是空的
            if (maxDataRow == -1 && maxDataColumn == -1) {
                return true;
            }

            // 进一步检查是否所有单元格都是空的
            boolean hasData = false;
            for (int row = 0; row <= maxDataRow && !hasData; row++) {
                for (int col = 0; col <= maxDataColumn && !hasData; col++) {
                    Cell cell = worksheet.getCells().get(row, col);
                    if (cell != null && cell.getType() != CellValueType.IS_NULL) {
                        String value = cell.getStringValue();
                        if (StringUtils.isNotBlank(value)) {
                            hasData = true;
                            log.debug("worksheet[{}]在位置[{},{}]发现数据: {}",
                                    worksheet.getName(), row, col,
                                    value.length() > 20 ? value.substring(0, 20) + "..." : value);
                        }
                    }
                }
            }

            return !hasData;
        } catch (Exception e) {
            log.warn("检查worksheet[{}]是否为空时发生异常: {}", worksheet.getName(), e.getMessage());
            // 发生异常时保守处理，认为不为空
            return false;
        }
    }


    private void getPrintingFile(ReportExcelDto reportExcelDto, String excelPath, FileOutputStream fileOS, int saveFormat) throws Exception {
        Workbook wb = new Workbook(excelPath);

        log.debug("开始PDF转换，Excel文件路径: {}", excelPath);
        log.debug("Workbook包含{}个worksheet", wb.getWorksheets().getCount());

        // 移除空白的worksheet以避免生成空白PDF页
        removeEmptyWorksheets(wb);

        PdfSaveOptions pdfSaveOptions = new PdfSaveOptions();
        // 设置打印页面范围
        if (Objects.nonNull(reportExcelDto.getPageRange()) && reportExcelDto.getPageRange().length > 0) {
            pdfSaveOptions.setPageIndex(reportExcelDto.getPageRange()[0]);
            // 结束页码减去开始页码加一 得出总页数
            pdfSaveOptions.setPageCount(reportExcelDto.getPageRange()[1] - reportExcelDto.getPageRange()[0] + 1);
        }
        //多sheet情况
        Boolean hasSomeWork = wb.getWorksheets().getCount() > 1;
        for (int i = 0; i < wb.getWorksheets().getCount(); i++) {
            Worksheet worksheet = wb.getWorksheets().get(i);
            boolean heFlag = worksheet.getName().contains("#hx");//特定sheet页横向打印
//                    // 自动调整列宽度
//                    worksheet.autoFitColumns();
//                    // 自动调整行高
//                    worksheet.autoFitRows();
            if (reportExcelDto.getRange() != null
                    && reportExcelDto.getRange().length == 4) {
                int startRow = reportExcelDto.getRange()[0];
                int startColumn = reportExcelDto.getRange()[1];
                int totalRows = reportExcelDto.getRange()[2];
                int totalColumns = reportExcelDto.getRange()[3];
                worksheet.getCells().deleteRows(startRow + totalRows, worksheet.getCells().getMaxRow() - totalRows);
                if (startRow != 0) {
                    worksheet.getCells().deleteRows(0, startRow);
                }
                worksheet.getCells().deleteColumns(startColumn + totalColumns, worksheet.getCells().getMaxColumn() - totalColumns, true);
                if (startColumn != 0) {
                    worksheet.getCells().deleteColumns(0, startColumn, true);
                }

            }

            PageSetup pageSetup = worksheet.getPageSetup();
            // 自定义长宽高
            Double[] customPaperSize = reportExcelDto.getCustomPaperSize();
            if (customPaperSize != null && customPaperSize.length == 2) {
                pageSetup.customPaperSize(customPaperSize[0], customPaperSize[1]);
            } else {
                // 设置纸张大小
                pageSetup.setPaperSize(reportExcelDto.getPaperSizeType() != null ? reportExcelDto.getPaperSizeType() : PaperSizeType.PAPER_A_4);
            }

            // 设置打印方向
            pageSetup.setOrientation(reportExcelDto.getOrientation() != null ? reportExcelDto.getOrientation() : PageOrientationType.PORTRAIT);
            if (heFlag) {
                pageSetup.setOrientation(PageOrientationType.LANDSCAPE);
            }
            // 是否设置页码
            if (Objects.nonNull(reportExcelDto.getPrintFooterPageNum()) && reportExcelDto.getPrintFooterPageNum()) {
                pageSetup.setFooter(1, reportExcelDto.getHasHomePageCover() ? "第&P页 共&N-1页" : "第&P页 共&N页");
                if (hasSomeWork && i == 0) {
                    // 第一个sheet中设置第一个也无页脚
                    if (reportExcelDto.getHasHomePageCover()) {
                        pageSetup.setFirstPageNumber(0);
                        // 设置第一页页脚于其他页不一样
                        pageSetup.setHFDiffFirst(true);
                        // 将第一页页脚置空
                        pageSetup.setFirstPageFooter(1, "");
                    }
                } else {
                    // 非多sheet
                    if (reportExcelDto.getHasHomePageCover()) {
                        if (hasSomeWork) {
                            pageSetup.setAutoFirstPageNumber(true);
                        } else {

                            pageSetup.setFirstPageNumber(0);
                        }
                        // 设置第一页页脚于其他页不一样
                        pageSetup.setHFDiffFirst(true);
                        // 将第一页页脚置空
                        pageSetup.setFirstPageFooter(1, "");
                    }
                }
            }

            // 设置布局方式
            if (Objects.nonNull(reportExcelDto.getPrintLayoutType())) {
                switch (reportExcelDto.getPrintLayoutType()) {
                    case 1:
                        pageSetup.setCenterHorizontally(true);
                        break;//横向居中
                    case 2:
                        pageSetup.setCenterVertically(true);
                        break;//垂直居中
                    case 3:
                        pageSetup.setCenterHorizontally(true);
                        pageSetup.setCenterVertically(true);
                        break;//垂直居中
                    default:
                        break;
                }
            }
            // 上左下右边距
            if (Objects.nonNull(reportExcelDto.getMargin()) && reportExcelDto.getMargin().length == 4) {
                Double[] margin = reportExcelDto.getMargin();
                pageSetup.setTopMarginInch(margin[0]);
                pageSetup.setLeftMarginInch(margin[1]);
                pageSetup.setBottomMarginInch(margin[2]);
                pageSetup.setRightMarginInch(margin[3]);
            }

            if (Objects.nonNull(reportExcelDto.getPrintGridLines())) {
                // 是否需要显示网格列
                pageSetup.setPrintGridlines(reportExcelDto.getPrintGridLines());
            }
            if (Objects.nonNull(reportExcelDto.getPrintCopies())) {
                // 打印份数
                pageSetup.setPrintCopies(reportExcelDto.getPrintCopies());
            }
            if (Objects.nonNull(reportExcelDto.getZoom())) {
                // 缩放比例
                pageSetup.setZoom(reportExcelDto.getZoom());
            }

        }

        log.debug("PDF转换配置完成，最终worksheet数量: {}", wb.getWorksheets().getCount());
        for (int i = 0; i < wb.getWorksheets().getCount(); i++) {
            Worksheet ws = wb.getWorksheets().get(i);
            log.debug("worksheet[{}]: 名称={}, 数据行数={}, 数据列数={}",
                    i, ws.getName(), ws.getCells().getMaxDataRow() + 1, ws.getCells().getMaxDataColumn() + 1);
        }

        wb.save(fileOS, saveFormat);
        fileOS.flush();

        log.debug("PDF转换完成");
    }

    /**
     * 统一调用接口查询数据
     */
    @Override
    public PreviewVO getOriginalDataMap(ReportExcelDto reportExcelDto) {

        PreviewVO previewVO = new PreviewVO();
        previewVO.setTotal(1); //设置总数默认值
        Map<String, OriginalDataDto> results = new HashMap<>();
        //前端参数map
        Map<String, String> paramMap = new HashMap<>();
        setSysParam(paramMap);
        previewVO.setParamMap(paramMap);

        //校验是否解析sheet
        Set<String> checkKeys = new HashSet<>();
        previewVO.setCheckKeys(checkKeys);

        String setParam = reportExcelDto.getReportParam();
        boolean analyse = false;
        JSONObject setParamJson = new JSONObject();
        if (StringUtils.isNotBlank(setParam)) {
            setParamJson = JSON.parseObject(setParam);
            analyse = true;
        }

        Map<String, Object> reqParam = new HashMap<>();
        // 公共参数插入到接口参数中
        if (setParamJson.containsKey("common")) {
            setParamJson.getJSONArray("common").stream().map(e -> (JSONObject) e).forEach(json -> {
                setPageParam(reqParam, json, paramMap);
            });
        }
        Map<String, String> errMsgMap = new HashMap<>();
        previewVO.setErrMsgMap(errMsgMap);
        // 是否有接口需要调用
        if (StringUtils.isNotBlank(reportExcelDto.getReportInterfacesIds())) {
            String[] apiIds = reportExcelDto.getReportInterfacesIds().split(",");
            ArrayList<Future<OriginalDataDto>> tasks = new ArrayList<>();
            AtomicReference<Integer> totalMax = new AtomicReference<>(0);

            MagicHttpServletRequest request = WebUtils.magicRequestContextHolder.getRequest();
            MagicHttpServletResponse response = WebUtils.magicRequestContextHolder.getResponse();

            for (String apiId : apiIds) {
                boolean finalAnalyse = analyse;
                JSONObject finalSetParamJson = setParamJson;

                RequestAttributes attributes = RequestContextHolder.getRequestAttributes();

                tasks.add(threadPool.submit(() -> {
                    //上下文传递给新线程
                    RequestContextHolder.setRequestAttributes(attributes);
                    OriginalDataDto originalDataDto = new OriginalDataDto();
                    originalDataDto.setApiId(apiId);

                    Map<String, Object> param = new HashMap<>();
                    //设置默认分页参数
                    setDefaultPage(param);

                    //Map<String, String> magicApi = iMagicApiFileService.interfacesUriById(apiId);
                    if (finalAnalyse) {
                        try {
                            // 拼接接口参数
                            if (finalSetParamJson.containsKey(apiId)) {
                                JSONArray apiParam = finalSetParamJson.getJSONArray(apiId);
                                apiParam.stream().map(e -> (JSONObject) e).forEach(json -> {
                                    setPageParam(param, json, paramMap);
                                });
                            }

                            param.putAll(reqParam);

                        } catch (Exception e) {
                            log.error("报表解析参数报错", e);
                        }
                    }
                    try {
                        Object execute = ldfInterfacesInfoService.executeApi(request, response, apiId, param);
                       /* Object execute = magicAPIService.execute(magicApi.get("method"), magicApi.get("url"), magicApi.get("method").equals("POST") ? new HashMap<String, Object>() {{
                            put("body", param);
                        }} : param);*/
                        if (execute instanceof Map) {
                            List<Object> jsonObjects = new ArrayList<>();
                            JSONObject apiResult = JSON.parseObject(JSON.toJSONString(execute));
                            if (apiResult.containsKey("records") && apiResult.containsKey("info")) {
                                jsonObjects.add(apiResult);
                                Integer total = apiResult.getInteger("total");
                                total = total != null ? total : 0;
                                originalDataDto.setTotal(total);
                                if (total > totalMax.get()) {
                                    totalMax.set(total);
                                }
                            } else if (apiResult.containsKey("records")) {
                                jsonObjects.addAll(apiResult.getJSONArray("records"));
                                Integer total = apiResult.getInteger("total");
                                total = total != null ? total : 0;
                                originalDataDto.setTotal(total);
                                if (total > totalMax.get()) {
                                    totalMax.set(total);
                                }
                            } else if (apiResult.containsKey("lineChart")) {
                                jsonObjects.add(apiResult);
                            } else {
                                jsonObjects.add(apiResult);
                                // 设置解析数据 需要校验的字段名称
                                apiResult.forEach((s, o) -> {
                                    if (o instanceof JSONArray && !((JSONArray) o).isEmpty()) {
                                        checkKeys.add(s);
                                    }
                                });

                            }
                            originalDataDto.setData(jsonObjects);
                        } else if (execute instanceof ExitValue) {
                            Object[] res = ((ExitValue) execute).getValues();
                            if (res[0] instanceof Integer && res[0].equals(500)) {
                                errMsgMap.put(apiId, res[1].toString());
                            }
                        } else {
                            originalDataDto.setData((List<Object>) execute);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    return originalDataDto;
                }));
            }

            tasks.forEach(task -> {
                try {
                    OriginalDataDto originalDataDto = task.get();
                    results.put(originalDataDto.getApiId(), originalDataDto);
                    List<Object> dataList = originalDataDto.getData();
                    if (dataList != null && !dataList.isEmpty()) {
                        Object o = dataList.get(0);
                        JSONObject jObj = JSONObject.parseObject(JSON.toJSONString(o));
                        //将带有info标识的数据加入参数列表
                        if (jObj.containsKey("info")) {
                            Object infoObj = jObj.get("info");
                            if (infoObj instanceof JSONObject) {
                                JSONObject info = (JSONObject) infoObj;
                                info.keySet().forEach(s -> {
                                    String key = originalDataDto.getApiId().concat(".").concat("info").concat(".").concat(s);
                                    paramMap.put(key, info.getString(s));
                                });
                            }
                        }
                    }

                } catch (InterruptedException | ExecutionException e) {
                    e.printStackTrace();
                }
            });
            if (totalMax.get() > 0) {
                previewVO.setTotal(totalMax.get());
            }
            previewVO.setResults(results);
        }
        return previewVO;
    }

    private void setSysParam(Map<String, String> paramMap) {

        LoginUser loginUser = tokenService.getLoginUser();
        if (loginUser != null && loginUser.getSysUser() != null) {
            paramMap.put("user", loginUser.getSysUser().getNickName());
        }
        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern(ParamConstants.DATE_PATTERN_DEFAULT));
        paramMap.put("date", currentTime);
    }

    /**
     * paramMap机构
     * apiId1 --> {"参数名","参数值"}
     * apiId2 --> {"参数名","参数值"}
     */

    private void setPageParam(Map<String, Object> param, JSONObject json, Map<String, String> paramMap) {
        String name = json.getString("name");
        if ("pageSize".equals(name)) {
            try {
                String pageSize = json.getString("value");
                if (ParamConstants.S_PRINT_MAX.equals(pageSize)) {
                    param.put(name, ParamConstants.PRINT_MAX);
                } else if (ParamConstants.S_EXPORT_MAX.equals(pageSize)) {
                    param.put(name, ParamConstants.EXPORT_MAX);
                } else {
                    param.put(name, Integer.parseInt(pageSize));
                }
            } catch (Exception e) {
                param.put(name, json.get("value"));
            }
        } else if ("pageNum".equals(name)) {
            try {
                param.put(name, Integer.parseInt(json.getString("value")));
            } catch (Exception e) {
                param.put(name, json.get("value"));
            }
        } else if ("daterange".equals(name)) {
            Object dates = json.get("value");
            if (dates instanceof JSONArray) {
                JSONArray dateRange = (JSONArray) dates;
                if (!dateRange.isEmpty()) {
                    Date startTime = dateRange.getDate(0);
                    Date endTime = dateRange.getDate(1);
                    SimpleDateFormat format = new SimpleDateFormat(ParamConstants.DATE_PATTERN_DEFAULT);
                    String start = format.format(startTime);
                    String end = format.format(endTime);
                    param.put("daterange", start + "," + end);
                    paramMap.put("startTime", start);
                    paramMap.put("endTime", end);
                }
            } else if (dates instanceof String) {
                try {
                    String dateStr = dates.toString();
                    String[] date = dateStr.split(",");
                    if (date.length > 1) {
                        String start = date[0];
                        String end = date[1];
                        param.put("daterange", start + "," + end);
                        paramMap.put("startTime", start);
                        paramMap.put("endTime", end);
                    }
                } catch (Exception e) {
                    log.error("设置时间参数出错:", e);
                }
            }
        } else {
            param.put(name, json.get("value"));
            paramMap.put(name, json.getString("value"));
        }


    }

    private void setDefaultPage(Map<String, Object> param) {
        //设置分页默认值 暂时解决未传参报错问题
        if (!param.containsKey("pageNum") || "".equals(param.get("pageNum"))) {
            param.put("pageNum", 1);
        }
        if (!param.containsKey("pageSize") || "".equals(param.get("pageSize"))) {
            param.put("pageSize", 10);
        }
    }

    /**
     * 解析报表数据，动态插入列表数据和对象数据
     */
    private String analysisReportData(ReportExcelDto reportExcelDto) {

        String jsonStr = reportExcelDto.getReportContent();
        List<JSONObject> dbObjectList = (List<JSONObject>) JSON.parse(jsonStr);
        //增加分页接口总数
        PreviewVO previewVO = getOriginalDataMap(reportExcelDto);
        reportExcelDto.setErrMsgMap(previewVO.getErrMsgMap());
        reportExcelDto.setTotal(previewVO.getTotal());
        String setParam = reportExcelDto.getReportParam();
        boolean pageSheet = false;
        //特殊 多个封面+数据形式
        if (Objects.nonNull(dbObjectList) && dbObjectList.size() > 1) {
            JSONObject cover = null;
            JSONObject content = null;
            for (JSONObject jsonObject : dbObjectList) {
                String name = jsonObject.getString("name");
                if ("sheet##Cover".equals(name)) {
                    cover = jsonObject;
                }
                if ("sheet##Data".equals(name)) {
                    content = jsonObject;
                }
            }


            if (cover != null && content != null) {

                Map<String, OriginalDataDto> dataMap = (Map<String, OriginalDataDto>) previewVO.getResults();
                Set<String> strings = dataMap.keySet();
                Object key = strings.toArray()[0];

                OriginalDataDto originalData = dataMap.get(key);

                List<Object> data = originalData.getData();

                SheetContext sheetContext = new SheetContext(dbObjectList, cover, content, data, key.toString(), setParam, analysisSheetService);
                try {
                    dbObjectList = sheetContext.get();
                } catch (Exception e) {
                    e.printStackTrace();
                }

                //fastjson $ref 循环引用
                return JSONObject.toJSONString(dbObjectList, SerializerFeature.DisableCircularReferenceDetect);
            }
            //循环校验去除没有动态数据的sheet
            for (int i = dbObjectList.size() - 1; i >= 0; i--) {
                JSONObject sheet = dbObjectList.get(i);
                String sheetName = sheet.getString("name");
                if (StringUtils.isNotBlank(sheetName)) {
                    String[] check = sheetName.split("##");
                    if (check.length > 1) {
                        String rule = check[1];
                        //支持多页 多sheet解析
                        if (rule.startsWith("Page") && i == 0) {
                            String[] ruleArr = rule.split("#");
                            Integer size = Integer.parseInt(ruleArr[1]);
                            Map<String, OriginalDataDto> dataMap = (Map<String, OriginalDataDto>) previewVO.getResults();
                            Set<String> strings = dataMap.keySet();
                            Object key = strings.toArray()[0];

                            OriginalDataDto originalData = dataMap.get(key);
                            List<Object> data = originalData.getData();

                            JSONObject pageInfo = null;
                            String newListKey = null;

                            if (data != null && !data.isEmpty()) {
                                Object o = data.get(0);
                                if (o instanceof JSONObject) {
                                    JSONObject jsonO = (JSONObject) o;
                                    if (jsonO.containsKey("current") && jsonO.containsKey("records")) {
                                        data = jsonO.getJSONArray("records");
                                        pageInfo = jsonO;
                                        newListKey = "records";
                                    } else if (jsonO.containsKey("info") && jsonO.size() == 2) {
                                        //info + 单list列表数据
                                        Set<String> json0Keys = jsonO.keySet();
                                        for (String json0Key : json0Keys) {
                                            if (!"info".equals(json0Key)) {
                                                data = jsonO.getJSONArray(json0Key);
                                                pageInfo = jsonO;
                                                newListKey = json0Key;
                                            }
                                        }
                                    }

                                }
                            }

                            Map<String, List<Object>> listMap = new HashMap<>();
                            List<Object> tempList = new ArrayList<>();
                            int count = 0;
                            int num = 0;//用于每页数据的计数
                            if (data != null && !data.isEmpty()) {
                                for (int i1 = 0; i1 < data.size(); i1++) {
                                    num++;
                                    Object o = data.get(i1);
                                    tempList.add(o);
                                    String listKey = count + "";
                                /*List<Object> list = null;
                                if (listMap.containsKey(listKey)){
                                    list = listMap.get(listKey);
                                }else {
                                    list = new ArrayList<>();
                                    listMap.put(listKey, list);
                                }*/
                                    if (num % size == 0) {
                                        JSONArray newList = JSON.parseArray(JSON.toJSONString(tempList));
                                        listMap.put(listKey, newList);
                                        count++;
                                        tempList.clear();//清空当前页数据缓存
                                        num = 0;//重置计数数量
                                    }
                                }
                                //剩余数据加入
                                if (!tempList.isEmpty()) {
                                    String listKey = count + "";
                                    JSONArray newList = JSON.parseArray(JSON.toJSONString(tempList));
                                    listMap.put(listKey, newList);
                                }
                            }
                            int pages = 0;
                            if (data != null && data.size() > 0) {
                                pages = (int) Math.ceil(data.size() / (double) size);//计算页数
                            }
                            //多页进行数据设置
                            if (pages > 0) {
                                for (int i1 = 0; i1 < pages; i1++) {
                                    JSONObject newSheet = JSON.parseObject(sheet.toJSONString());
                                    Integer index = newSheet.getInteger("index");
                                    Integer newIndex = index + i1;
                                    newSheet.put("index", newIndex);
                                    Long order = newSheet.getLong("order");
                                    newSheet.put("order", i1);
                                    newSheet.put("name", "page" + (i1 + 1));

                                    PreviewVO previewVO1 = new PreviewVO();
                                    dozerUtil.map(previewVO, previewVO1);
                                    String listKey = i1 + "";
                                    List<Object> pageList = listMap.get(listKey);
                                    Map<String, BigDecimal> sumMap = new HashMap<>();
                                    Map<String, MultiPageCalculate> calculateMap = new HashMap<>();
                                    //计算总数
                                    for (Object o : pageList) {
                                        if (o instanceof JSONObject) {
                                            JSONObject sum = (JSONObject) o;
                                            Set<String> sumKeys = sum.keySet();
                                            sumKeys.forEach(s1 -> {
                                                Object o1 = sum.get(s1);
                                                try {
                                                    String sumKey = s1 + "Total";
                                                    if (sumMap.containsKey(sumKey)) {
                                                        BigDecimal bigDecimal = sumMap.get(sumKey);
                                                        BigDecimal add = bigDecimal.add(new BigDecimal(o1.toString()));
                                                        sumMap.put(sumKey, add);
                                                        MultiPageCalculate calculate = calculateMap.get(s1);
                                                        calculate.setCount(calculate.getCount() + 1);
                                                        calculate.setTotal(add);
                                                        calculateMap.put(s1, calculate);
                                                    } else {
                                                        BigDecimal bigDecimal = new BigDecimal(o1.toString());
                                                        sumMap.put(sumKey, bigDecimal);
                                                        MultiPageCalculate calculate = new MultiPageCalculate();
                                                        calculate.setCount(1);
                                                        calculate.setTotal(bigDecimal);
                                                        calculateMap.put(s1, calculate);
                                                    }
                                                } catch (Exception e) {
                                                }
                                            });
                                        }
                                    }
                                    //计算平均值
                                    Map<String, BigDecimal> avgMap = new HashMap<>();
                                    calculateMap.forEach((s1, calculate) -> {
                                        String avgKey = s1 + "Avg";
                                        BigDecimal total = calculate.getTotal();
                                        Integer calCount = calculate.getCount();
                                        BigDecimal divide = total.divide(new BigDecimal(calCount), 2, BigDecimal.ROUND_HALF_UP);
                                        avgMap.put(avgKey, divide);
                                    });

                                    previewVO1.setResults(pageList);
                                    if (pageInfo != null) {
                                        JSONObject newData = JSON.parseObject(JSON.toJSONString(pageInfo));
                                        JSONObject info = newData.getJSONObject("info");
                                        if (info == null) {
                                            info = new JSONObject();
                                        }
                                        info.putAll(sumMap);
                                        info.putAll(avgMap);
                                        info.put("size", pageList.size());
                                        newData.put(newListKey, pageList);
                                        OriginalDataDto dataDto = new OriginalDataDto();
                                        JSONArray dataList = new JSONArray();
                                        dataList.add(newData);
                                        dataDto.setData(dataList);
                                        dataDto.setApiId(key.toString());
                                        Integer total = newData.getInteger("total");
                                        total = total == null ? 0 : total;
                                        dataDto.setTotal(total);
                                        Map<String, OriginalDataDto> newMap = new HashMap<>();
                                        newMap.put(key.toString(), dataDto);

                                        previewVO1.setResults(newMap);
                                    }
                                    pageHandler.analysisSheetCellData(newSheet, setParam, previewVO1);
                                    dbObjectList.add(newSheet);
                                }
                                pageSheet = true;
                                dbObjectList.remove(sheet);
                            }
                        } else if (rule.startsWith("DataSize") && i == 0) {


                            Map<String, OriginalDataDto> dataMap = (Map<String, OriginalDataDto>) previewVO.getResults();
                            Set<String> strings = dataMap.keySet();
                            Object key = strings.toArray()[0];

                            OriginalDataDto originalData = dataMap.get(key);
                            List<Object> data = originalData.getData();

                            int pages = data.size();

                            //多页进行数据设置
                            if (pages > 0) {
                                for (int i1 = 0; i1 < pages; i1++) {
                                    JSONObject newSheet = JSON.parseObject(sheet.toJSONString());
                                    String index = newSheet.getString("index");
                                    String[] s = index.split("_");
                                    Long newNum = Long.parseLong(s[2]) + i1;
                                    String newIndex = s[0] + "_" + s[1] + "_" + newNum;
                                    newSheet.put("index", newIndex);
                                    Long order = newSheet.getLong("order");
                                    newSheet.put("order", i1);
                                    newSheet.put("name", "sheet" + (i1 + 1));

                                    PreviewVO previewVO1 = new PreviewVO();
                                    dozerUtil.map(previewVO, previewVO1);

                                    JSONObject newData = JSON.parseObject(JSON.toJSONString(data.get(i1)));

                                    OriginalDataDto dataDto = new OriginalDataDto();
                                    JSONArray dataList = new JSONArray();
                                    dataList.add(newData);
                                    dataDto.setData(dataList);
                                    dataDto.setApiId(key.toString());
                                    Integer total = newData.getInteger("total");
                                    total = total == null ? 0 : total;
                                    dataDto.setTotal(total);
                                    Map<String, OriginalDataDto> newMap = new HashMap<>();
                                    newMap.put(key.toString(), dataDto);

                                    previewVO1.setResults(newMap);

                                    pageHandler.analysisSheetCellData(newSheet, setParam, previewVO1);
                                    dbObjectList.add(newSheet);
                                }
                                pageSheet = true;
                                dbObjectList.remove(sheet);
                            }
                        } else {
                            Set<String> checkKeys = previewVO.getCheckKeys();
                            if (!checkKeys.contains(rule)) {
                                dbObjectList.remove(i);
                            }
                        }
                    }
                }
            }
        }



        if (!pageSheet) {
            // 循环存在的sheet
            if (dbObjectList != null && dbObjectList.size() > 0) {
                for (int x = 0; x < dbObjectList.size(); x++) {
                    analysisSheetService.analysisSheetCellData(dbObjectList.get(x), setParam, previewVO);
                }
            }
        }
        //fastjson $ref 循环引用
        return JSONObject.toJSONString(dbObjectList, SerializerFeature.DisableCircularReferenceDetect);
    }

    /**
     * 动态参数替换
     *
     * @param setParam
     * @param dto
     */
    private void getContextData(String setParam, DataSetDto dto) {
        if (StringUtils.isNotBlank(setParam)) {
            JSONObject setParamJson = JSONObject.parseObject(setParam);
            Map<String, Object> map = new HashMap<>();
            // 查询条件
            if (setParamJson.containsKey(dto.getSetCode())) {
                JSONObject paramCondition = setParamJson.getJSONObject(dto.getSetCode());
                paramCondition.forEach(map::put);
            }
            dto.setContextData(map);
        }
    }


    /**
     * mongodb缓存
     */
    public GaeaReportExcel getOne(String reportCode, Wrapper<GaeaReportExcel> queryWrapper, boolean throwEx) {
        Query query = new Query(Criteria.where(MongoConstants.REPORT_CODE).is(reportCode));
        GaeaReportExcel gaeaReportExcel = mongoTemplate.findOne(query, GaeaReportExcel.class);
        if (gaeaReportExcel != null) {
            return gaeaReportExcel;
        }
        gaeaReportExcel = this.getOne(queryWrapper, throwEx);
        if (gaeaReportExcel != null) {
            return mongoTemplate.save(gaeaReportExcel);
        }
        return gaeaReportExcel;
    }

    @Override
    public boolean updateById(GaeaReportExcel entity) {
        boolean update = super.updateById(entity);
        if (update) {
            //删除mongodb旧缓存 添加新缓存
            Query delete = new Query(Criteria.where(MongoConstants.REPORT_CODE).is(entity.getReportCode()));
            mongoTemplate.remove(delete, GaeaReportExcel.class);
            mongoTemplate.save(entity);
        }
        return update;
    }

    @Override
    public boolean saveOrUpdate(GaeaReportExcel entity, Wrapper<GaeaReportExcel> updateWrapper) {
        boolean saveOrUpdate = super.saveOrUpdate(entity, updateWrapper);
        if (saveOrUpdate) {
            mongoTemplate.save(entity);
        }
        return saveOrUpdate;
    }

}
