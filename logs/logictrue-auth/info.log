00:47:34.678 [scheduling-1] INFO  c.l.a.c.WxConnectConfig - [builderAccessToken,73] - java.lang.NullPointerException
00:47:34.681 [scheduling-1] INFO  c.l.a.c.WxConnectConfig - [builderAccessToken,73] - java.lang.NullPointerException
00:47:34.682 [scheduling-1] INFO  c.l.a.c.WxConnectConfig - [builderAccessToken,73] - java.lang.NullPointerException
01:49:31.846 [scheduling-1] INFO  c.l.a.c.WxConnectConfig - [builderAccessToken,73] - java.lang.NullPointerException
01:49:31.848 [scheduling-1] INFO  c.l.a.c.WxConnectConfig - [builderAccessToken,73] - java.lang.NullPointerException
01:49:31.850 [scheduling-1] INFO  c.l.a.c.WxConnectConfig - [builderAccessToken,73] - java.lang.NullPointerException
09:21:10.468 [nacos-grpc-client-executor-1860] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5edde58-9413-4575-abb2-f67f1ab8b71a_config-0]receive server push request,request=ClientDetectionRequest,requestId=57
09:21:10.468 [nacos-grpc-client-executor-1922] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79f562a5-9184-4a63-b2d7-e66a4be9275e]receive server push request,request=ClientDetectionRequest,requestId=56
09:21:10.470 [nacos-grpc-client-executor-1860] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b5edde58-9413-4575-abb2-f67f1ab8b71a_config-0]ack server push request,request=ClientDetectionRequest,requestId=57
09:21:10.470 [nacos-grpc-client-executor-1922] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [79f562a5-9184-4a63-b2d7-e66a4be9275e]ack server push request,request=ClientDetectionRequest,requestId=56
10:15:39.190 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.0.Final
10:15:40.714 [main] INFO  c.a.n.c.c.i.LocalConfigInfoProcessor - [<clinit>,67] - LOCAL_SNAPSHOT_PATH:/home/<USER>/nacos/config
10:15:40.717 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,77] - [RpcClientFactory] create a new rpc client of 9ad80f2f-4dc6-4804-8f41-a397e2ee432d_config-0
10:15:40.769 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 3 keys and 6 values 
10:15:40.792 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
10:15:40.801 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
10:15:40.950 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 146 ms to scan 228 urls, producing 0 keys and 0 values 
10:15:40.961 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
10:15:40.983 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 1 keys and 7 values 
10:15:40.997 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
10:15:41.123 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 124 ms to scan 228 urls, producing 0 keys and 0 values 
10:15:41.125 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ad80f2f-4dc6-4804-8f41-a397e2ee432d_config-0]RpcClient init label, labels={module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:15:41.126 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ad80f2f-4dc6-4804-8f41-a397e2ee432d_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$331/1970073944
10:15:41.126 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ad80f2f-4dc6-4804-8f41-a397e2ee432d_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$332/1060042118
10:15:41.127 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ad80f2f-4dc6-4804-8f41-a397e2ee432d_config-0]Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:15:41.127 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ad80f2f-4dc6-4804-8f41-a397e2ee432d_config-0]RpcClient init, ServerListFactory =com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:15:41.133 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ad80f2f-4dc6-4804-8f41-a397e2ee432d_config-0] Try to connect to server on start up, server: {serverIp='127.0.0.1', server main port=8848}
10:15:41.777 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ad80f2f-4dc6-4804-8f41-a397e2ee432d_config-0] Success to connect to server [127.0.0.1:8848] on start up,connectionId=1755915341521_172.21.0.1_46818
10:15:41.778 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ad80f2f-4dc6-4804-8f41-a397e2ee432d_config-0]Notify connected event to listeners.
10:15:41.778 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,681] - [9ad80f2f-4dc6-4804-8f41-a397e2ee432d_config-0] Connected,notify listen context...
10:15:41.778 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ad80f2f-4dc6-4804-8f41-a397e2ee432d_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:15:41.779 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ad80f2f-4dc6-4804-8f41-a397e2ee432d_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$4
10:15:41.815 [main] INFO  c.a.n.c.c.i.Limiter - [<clinit>,56] - limitTime:5.0
10:15:41.868 [main] INFO  c.a.n.c.c.u.JvmUtil - [<clinit>,53] - isMultiInstance:false
10:15:41.935 [main] INFO  c.l.a.LogicTrueAuthApplication - [logStartupProfileInfo,663] - The following profiles are active: dev
10:15:45.197 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9211"]
10:15:45.197 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:15:45.198 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
10:15:45.286 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:15:47.294 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.17.5
10:15:47.392 [redisson-netty-5-6] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
10:15:47.537 [redisson-netty-5-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
10:15:47.879 [main] INFO  c.a.c.s.SentinelWebAutoConfiguration - [addInterceptors,80] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:15:49.030 [main] INFO  c.a.n.client.naming - [initNamespaceForNaming,63] - initializer namespace from System Property : null
10:15:49.031 [main] INFO  c.a.n.client.naming - [call,69] - initializer namespace from System Environment :null
10:15:49.032 [main] INFO  c.a.n.client.naming - [call,79] - initializer namespace from System Property :null
10:15:49.045 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,77] - [RpcClientFactory] create a new rpc client of c88ecd00-3f1d-4097-bb76-873ad14cfe4f
10:15:49.045 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c88ecd00-3f1d-4097-bb76-873ad14cfe4f]RpcClient init label, labels={module=naming, source=sdk}
10:15:49.047 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c88ecd00-3f1d-4097-bb76-873ad14cfe4f]RpcClient init, ServerListFactory =com.alibaba.nacos.client.naming.core.ServerListManager
10:15:49.047 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c88ecd00-3f1d-4097-bb76-873ad14cfe4f]Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:15:49.048 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c88ecd00-3f1d-4097-bb76-873ad14cfe4f]Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:15:49.048 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c88ecd00-3f1d-4097-bb76-873ad14cfe4f] Try to connect to server on start up, server: {serverIp='127.0.0.1', server main port=8848}
10:15:49.162 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c88ecd00-3f1d-4097-bb76-873ad14cfe4f] Success to connect to server [127.0.0.1:8848] on start up,connectionId=1755915349054_172.21.0.1_44700
10:15:49.163 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c88ecd00-3f1d-4097-bb76-873ad14cfe4f]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:15:49.163 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c88ecd00-3f1d-4097-bb76-873ad14cfe4f]Notify connected event to listeners.
10:15:49.163 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
10:15:49.163 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c88ecd00-3f1d-4097-bb76-873ad14cfe4f]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$4
10:15:50.809 [main] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@logictrue-auth@@DEFAULT -> []
10:15:50.816 [main] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@logictrue-auth@@DEFAULT -> []
10:15:50.820 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9211"]
10:15:50.833 [main] INFO  c.a.n.client.naming - [registerService,112] - [REGISTER-SERVICE] dev registering service logictrue-auth with instance Instance{instanceId='null', ip='**************', port=9211, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
10:15:50.850 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP logictrue-auth **************:9211 register finished
10:15:51.362 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c88ecd00-3f1d-4097-bb76-873ad14cfe4f]receive server push request,request=NotifySubscriberRequest,requestId=1
10:15:51.367 [nacos-grpc-client-executor-9] INFO  c.a.n.client.naming - [isChangedServiceInfo,235] - new ips(1) service: DEFAULT_GROUP@@logictrue-auth@@DEFAULT -> [{"instanceId":"**************#9211#DEFAULT#DEFAULT_GROUP@@logictrue-auth","ip":"**************","port":9211,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-auth","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
10:15:51.367 [nacos-grpc-client-executor-9] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-auth@@DEFAULT -> [{"instanceId":"**************#9211#DEFAULT#DEFAULT_GROUP@@logictrue-auth","ip":"**************","port":9211,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-auth","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
10:15:51.368 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c88ecd00-3f1d-4097-bb76-873ad14cfe4f]ack server push request,request=NotifySubscriberRequest,requestId=1
10:15:51.912 [main] INFO  c.l.a.LogicTrueAuthApplication - [logStarted,61] - Started LogicTrueAuthApplication in 13.157 seconds (JVM running for 13.829)
10:15:51.917 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] logictrue-auth.yml+DEFAULT_GROUP+dev
10:15:51.918 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=dev, dataId=logictrue-auth.yml, group=DEFAULT_GROUP, cnt=1
10:15:51.918 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] logictrue-auth+DEFAULT_GROUP+dev
10:15:51.919 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=dev, dataId=logictrue-auth, group=DEFAULT_GROUP, cnt=1
10:15:51.919 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] logictrue-auth-dev.yml+DEFAULT_GROUP+dev
10:15:51.920 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=dev, dataId=logictrue-auth-dev.yml, group=DEFAULT_GROUP, cnt=1
10:15:52.244 [RMI TCP Connection(4)-127.0.0.1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:15:54.920 [scheduling-1] INFO  c.l.a.c.WxConnectConfig - [builderAccessToken,73] - java.lang.NullPointerException
10:15:54.922 [scheduling-1] INFO  c.l.a.c.WxConnectConfig - [builderAccessToken,73] - java.lang.NullPointerException
10:15:54.925 [scheduling-1] INFO  c.l.a.c.WxConnectConfig - [builderAccessToken,73] - java.lang.NullPointerException
