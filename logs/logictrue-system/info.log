00:00:24.886 [pool-7-thread-1] INFO  c.l.s.t.ApiLogTask - [lambda$configureTasks$0,26] - 执行定时任务: 00:00:24.886
00:00:24.911 [pool-7-thread-1] INFO  c.l.s.s.i.ApiLogServiceImpl - [delA<PERSON><PERSON><PERSON>,39] - 删除日志记录:0
00:00:24.912 [pool-7-thread-1] INFO  c.l.s.t.ApiLogTask - [lambda$configureTasks$1,33] - 定时删除日志记录cron表达式：0 0 0 ? * *
09:21:10.468 [nacos-grpc-client-executor-1880] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1079cfd9-444c-4c72-befd-46d3733fa81b]receive server push request,request=ClientDetectionRequest,requestId=60
09:21:10.468 [nacos-grpc-client-executor-1743] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0293a216-1b9d-4815-8b82-30982da930cd_config-0]receive server push request,request=ClientDetectionRequest,requestId=64
09:21:10.468 [nacos-grpc-client-executor-1803] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7abfacbd-f62b-42b4-b637-96404c324562_config-0]receive server push request,request=ClientDetectionRequest,requestId=65
09:21:10.473 [nacos-grpc-client-executor-1743] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0293a216-1b9d-4815-8b82-30982da930cd_config-0]ack server push request,request=ClientDetectionRequest,requestId=64
09:21:10.473 [nacos-grpc-client-executor-1880] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1079cfd9-444c-4c72-befd-46d3733fa81b]ack server push request,request=ClientDetectionRequest,requestId=60
09:21:10.473 [nacos-grpc-client-executor-1803] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7abfacbd-f62b-42b4-b637-96404c324562_config-0]ack server push request,request=ClientDetectionRequest,requestId=65
