00:00:24.886 [pool-7-thread-1] INFO  c.l.s.t.ApiLogTask - [lambda$configureTasks$0,26] - 执行定时任务: 00:00:24.886
00:00:24.911 [pool-7-thread-1] INFO  c.l.s.s.i.ApiLogServiceImpl - [delA<PERSON><PERSON><PERSON>,39] - 删除日志记录:0
00:00:24.912 [pool-7-thread-1] INFO  c.l.s.t.ApiLogTask - [lambda$configureTasks$1,33] - 定时删除日志记录cron表达式：0 0 0 ? * *
09:21:10.468 [nacos-grpc-client-executor-1880] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1079cfd9-444c-4c72-befd-46d3733fa81b]receive server push request,request=ClientDetectionRequest,requestId=60
09:21:10.468 [nacos-grpc-client-executor-1743] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0293a216-1b9d-4815-8b82-30982da930cd_config-0]receive server push request,request=ClientDetectionRequest,requestId=64
09:21:10.468 [nacos-grpc-client-executor-1803] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7abfacbd-f62b-42b4-b637-96404c324562_config-0]receive server push request,request=ClientDetectionRequest,requestId=65
09:21:10.473 [nacos-grpc-client-executor-1743] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0293a216-1b9d-4815-8b82-30982da930cd_config-0]ack server push request,request=ClientDetectionRequest,requestId=64
09:21:10.473 [nacos-grpc-client-executor-1880] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1079cfd9-444c-4c72-befd-46d3733fa81b]ack server push request,request=ClientDetectionRequest,requestId=60
09:21:10.473 [nacos-grpc-client-executor-1803] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7abfacbd-f62b-42b4-b637-96404c324562_config-0]ack server push request,request=ClientDetectionRequest,requestId=65
10:15:41.748 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.0.Final
10:15:43.240 [main] INFO  c.a.n.c.c.i.LocalConfigInfoProcessor - [<clinit>,67] - LOCAL_SNAPSHOT_PATH:/home/<USER>/nacos/config
10:15:43.243 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,77] - [RpcClientFactory] create a new rpc client of fe0eda05-b4cd-4e5d-a14d-3e8ffab35d89_config-0
10:15:43.307 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 50 ms to scan 1 urls, producing 3 keys and 6 values 
10:15:43.351 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 29 ms to scan 1 urls, producing 4 keys and 9 values 
10:15:43.364 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
10:15:43.558 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 191 ms to scan 308 urls, producing 0 keys and 0 values 
10:15:43.570 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
10:15:43.581 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
10:15:43.593 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
10:15:43.781 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 185 ms to scan 308 urls, producing 0 keys and 0 values 
10:15:43.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe0eda05-b4cd-4e5d-a14d-3e8ffab35d89_config-0]RpcClient init label, labels={module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:15:43.786 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe0eda05-b4cd-4e5d-a14d-3e8ffab35d89_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$331/691779749
10:15:43.787 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe0eda05-b4cd-4e5d-a14d-3e8ffab35d89_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$332/37841489
10:15:43.788 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe0eda05-b4cd-4e5d-a14d-3e8ffab35d89_config-0]Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:15:43.788 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe0eda05-b4cd-4e5d-a14d-3e8ffab35d89_config-0]RpcClient init, ServerListFactory =com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:15:43.797 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe0eda05-b4cd-4e5d-a14d-3e8ffab35d89_config-0] Try to connect to server on start up, server: {serverIp='127.0.0.1', server main port=8848}
10:15:45.502 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe0eda05-b4cd-4e5d-a14d-3e8ffab35d89_config-0] Success to connect to server [127.0.0.1:8848] on start up,connectionId=1755915345261_172.21.0.1_46840
10:15:45.503 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe0eda05-b4cd-4e5d-a14d-3e8ffab35d89_config-0]Notify connected event to listeners.
10:15:45.503 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe0eda05-b4cd-4e5d-a14d-3e8ffab35d89_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:15:45.504 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,681] - [fe0eda05-b4cd-4e5d-a14d-3e8ffab35d89_config-0] Connected,notify listen context...
10:15:45.504 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe0eda05-b4cd-4e5d-a14d-3e8ffab35d89_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$4
10:15:45.547 [main] INFO  c.a.n.c.c.i.Limiter - [<clinit>,56] - limitTime:5.0
10:15:45.572 [main] INFO  c.a.n.c.c.u.JvmUtil - [<clinit>,53] - isMultiInstance:false
10:15:45.693 [main] INFO  c.l.s.LogicTrueSystemApplication - [logStartupProfileInfo,663] - The following profiles are active: dev
10:15:49.064 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9201"]
10:15:49.065 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:15:49.065 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
10:15:49.139 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:15:49.791 [main] INFO  o.m.driver.cluster - [info,71] - Cluster created with settings {hosts=[localhost:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
10:15:49.837 [cluster-ClusterId{value='68a9245547bb64003425959b', description='null'}-localhost:27017] INFO  o.m.d.connection - [info,71] - Opened connection [connectionId{localValue:2, serverValue:1907}] to localhost:27017
10:15:49.837 [cluster-rtt-ClusterId{value='68a9245547bb64003425959b', description='null'}-localhost:27017] INFO  o.m.d.connection - [info,71] - Opened connection [connectionId{localValue:1, serverValue:1906}] to localhost:27017
10:15:49.838 [cluster-ClusterId{value='68a9245547bb64003425959b', description='null'}-localhost:27017] INFO  o.m.driver.cluster - [info,71] - Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=25, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=14801533}
10:15:50.534 [main] INFO  o.m.d.connection - [info,71] - Opened connection [connectionId{localValue:3, serverValue:1913}] to localhost:27017
10:15:51.973 [main] INFO  c.a.n.client.naming - [initNamespaceForNaming,63] - initializer namespace from System Property : null
10:15:51.973 [main] INFO  c.a.n.client.naming - [call,69] - initializer namespace from System Environment :null
10:15:51.974 [main] INFO  c.a.n.client.naming - [call,79] - initializer namespace from System Property :null
10:15:51.987 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,77] - [RpcClientFactory] create a new rpc client of cf460f3c-fc5e-4207-abb0-b2798069f3fe
10:15:51.987 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf460f3c-fc5e-4207-abb0-b2798069f3fe]RpcClient init label, labels={module=naming, source=sdk}
10:15:51.990 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf460f3c-fc5e-4207-abb0-b2798069f3fe]RpcClient init, ServerListFactory =com.alibaba.nacos.client.naming.core.ServerListManager
10:15:51.990 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf460f3c-fc5e-4207-abb0-b2798069f3fe]Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:15:51.990 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf460f3c-fc5e-4207-abb0-b2798069f3fe]Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:15:51.991 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf460f3c-fc5e-4207-abb0-b2798069f3fe] Try to connect to server on start up, server: {serverIp='127.0.0.1', server main port=8848}
10:15:52.105 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf460f3c-fc5e-4207-abb0-b2798069f3fe] Success to connect to server [127.0.0.1:8848] on start up,connectionId=1755915351998_172.21.0.1_44724
10:15:52.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf460f3c-fc5e-4207-abb0-b2798069f3fe]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:15:52.106 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf460f3c-fc5e-4207-abb0-b2798069f3fe]Notify connected event to listeners.
10:15:52.106 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
10:15:52.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf460f3c-fc5e-4207-abb0-b2798069f3fe]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$4
10:15:52.371 [main] INFO  c.a.d.p.DruidDataSource - [init,995] - {dataSource-1} inited
10:15:55.976 [main] INFO  c.a.d.p.DruidDataSource - [init,995] - {dataSource-2,ldf_server} inited
10:15:56.891 [main] INFO  c.g.d.c.DozerBeanMapperBuilder - [build,544] - Initializing Dozer. Version: 6.4.0, Thread Name: main
10:15:56.892 [main] INFO  c.g.d.c.u.RuntimeUtils - [isOSGi,53] - OSGi support is false
10:15:56.898 [main] INFO  c.g.d.c.c.r.LegacyPropertiesSettingsResolver - [processFile,60] - Trying to find Dozer configuration file: dozer.properties
10:15:56.900 [main] INFO  c.g.d.c.c.r.LegacyPropertiesSettingsResolver - [processFile,63] - Failed to find dozer.properties via com.github.dozermapper.core.config.resolvers.LegacyPropertiesSettingsResolver.
10:15:57.722 [main] INFO  c.a.d.p.DruidDataSource - [init,995] - {dataSource-3,ldf_server} inited
10:15:58.238 [main] INFO  c.a.d.p.DruidDataSource - [init,995] - {dataSource-4,ldf_server_zyrm} inited
10:15:58.641 [main] INFO  c.a.d.p.DruidDataSource - [init,995] - {dataSource-5,ldf_server_zyrm} inited
10:16:02.754 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,77] - [RpcClientFactory] create a new rpc client of c0ce0c58-d28f-426a-a60b-301a2548d8f6_config-0
10:16:02.754 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ce0c58-d28f-426a-a60b-301a2548d8f6_config-0]RpcClient init label, labels={module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:16:02.754 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ce0c58-d28f-426a-a60b-301a2548d8f6_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$331/691779749
10:16:02.755 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ce0c58-d28f-426a-a60b-301a2548d8f6_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$332/37841489
10:16:02.755 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ce0c58-d28f-426a-a60b-301a2548d8f6_config-0]Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:16:02.755 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ce0c58-d28f-426a-a60b-301a2548d8f6_config-0]RpcClient init, ServerListFactory =com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:16:02.755 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ce0c58-d28f-426a-a60b-301a2548d8f6_config-0] Try to connect to server on start up, server: {serverIp='127.0.0.1', server main port=8848}
10:16:02.889 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ce0c58-d28f-426a-a60b-301a2548d8f6_config-0] Success to connect to server [127.0.0.1:8848] on start up,connectionId=1755915362784_172.21.0.1_56024
10:16:02.889 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ce0c58-d28f-426a-a60b-301a2548d8f6_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:16:02.889 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ce0c58-d28f-426a-a60b-301a2548d8f6_config-0]Notify connected event to listeners.
10:16:02.890 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c0ce0c58-d28f-426a-a60b-301a2548d8f6_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$4
10:16:02.890 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,681] - [c0ce0c58-d28f-426a-a60b-301a2548d8f6_config-0] Connected,notify listen context...
10:16:03.010 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.17.5
10:16:03.074 [redisson-netty-5-5] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
10:16:03.105 [redisson-netty-5-19] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
10:16:03.848 [main] INFO  c.a.c.s.SentinelWebAutoConfiguration - [addInterceptors,80] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:16:06.590 [main] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@logictrue-system@@DEFAULT -> []
10:16:06.600 [main] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@logictrue-system@@DEFAULT -> []
10:16:06.607 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9201"]
10:16:06.622 [main] INFO  c.a.n.client.naming - [registerService,112] - [REGISTER-SERVICE] dev registering service logictrue-system with instance Instance{instanceId='null', ip='**************', port=9201, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
10:16:06.629 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP logictrue-system **************:9201 register finished
10:16:07.179 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf460f3c-fc5e-4207-abb0-b2798069f3fe]receive server push request,request=NotifySubscriberRequest,requestId=5
10:16:07.184 [nacos-grpc-client-executor-13] INFO  c.a.n.client.naming - [isChangedServiceInfo,235] - new ips(1) service: DEFAULT_GROUP@@logictrue-system@@DEFAULT -> [{"instanceId":"**************#9201#DEFAULT#DEFAULT_GROUP@@logictrue-system","ip":"**************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
10:16:07.184 [nacos-grpc-client-executor-13] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-system@@DEFAULT -> [{"instanceId":"**************#9201#DEFAULT#DEFAULT_GROUP@@logictrue-system","ip":"**************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
10:16:07.184 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf460f3c-fc5e-4207-abb0-b2798069f3fe]ack server push request,request=NotifySubscriberRequest,requestId=5
10:16:07.616 [main] INFO  c.l.s.c.TenantDataSourceRefreshConfig - [start,35] - TenantDataSource数据源同步监听启动
10:16:08.718 [main] INFO  c.l.s.t.ApiLogTask - [lambda$configureTasks$1,33] - 定时删除日志记录cron表达式：0 0 0 ? * *
10:16:08.724 [main] INFO  c.l.s.LogicTrueSystemApplication - [logStarted,61] - Started LogicTrueSystemApplication in 27.365 seconds (JVM running for 27.854)
10:16:08.735 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] logictrue-system-dev.yml+DEFAULT_GROUP+dev
10:16:08.735 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=dev, dataId=logictrue-system-dev.yml, group=DEFAULT_GROUP, cnt=1
10:16:08.736 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] logictrue-system+DEFAULT_GROUP+dev
10:16:08.736 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=dev, dataId=logictrue-system, group=DEFAULT_GROUP, cnt=1
10:16:08.736 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] logictrue-system.yml+DEFAULT_GROUP+dev
10:16:08.736 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=dev, dataId=logictrue-system.yml, group=DEFAULT_GROUP, cnt=1
10:16:09.006 [RMI TCP Connection(3)-127.0.0.1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
