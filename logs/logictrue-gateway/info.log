00:14:30.914 [nacos-grpc-client-executor-691] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]receive server push request,request=NotifySubscriberRequest,requestId=12
00:14:30.915 [nacos-grpc-client-executor-691] INFO  c.a.n.client.naming - [isChangedServiceInfo,241] - removed ips(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
00:14:30.915 [nacos-grpc-client-executor-691] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@logictrue-interfaces -> []
00:14:30.921 [nacos-grpc-client-executor-691] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]ack server push request,request=NotifySubscriberRequest,requestId=12
00:15:21.387 [nacos-grpc-client-executor-711] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]receive server push request,request=NotifySubscriberRequest,requestId=13
00:15:21.388 [nacos-grpc-client-executor-711] INFO  c.a.n.client.naming - [isChangedServiceInfo,235] - new ips(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
00:15:21.389 [nacos-grpc-client-executor-711] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
00:15:21.389 [nacos-grpc-client-executor-711] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]ack server push request,request=NotifySubscriberRequest,requestId=13
00:22:00.570 [nacos-grpc-client-executor-841] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]receive server push request,request=NotifySubscriberRequest,requestId=15
00:22:00.571 [nacos-grpc-client-executor-841] INFO  c.a.n.client.naming - [isChangedServiceInfo,241] - removed ips(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
00:22:00.571 [nacos-grpc-client-executor-841] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@logictrue-interfaces -> []
00:22:00.572 [nacos-grpc-client-executor-841] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]ack server push request,request=NotifySubscriberRequest,requestId=15
00:22:49.921 [nacos-grpc-client-executor-858] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]receive server push request,request=NotifySubscriberRequest,requestId=16
00:22:49.922 [nacos-grpc-client-executor-858] INFO  c.a.n.client.naming - [isChangedServiceInfo,235] - new ips(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
00:22:49.922 [nacos-grpc-client-executor-858] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
00:22:49.922 [nacos-grpc-client-executor-858] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]ack server push request,request=NotifySubscriberRequest,requestId=16
00:32:12.163 [nacos-grpc-client-executor-1046] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]receive server push request,request=NotifySubscriberRequest,requestId=21
00:32:12.163 [nacos-grpc-client-executor-1046] INFO  c.a.n.client.naming - [isChangedServiceInfo,241] - removed ips(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
00:32:12.165 [nacos-grpc-client-executor-1046] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@logictrue-interfaces -> []
00:32:12.166 [nacos-grpc-client-executor-1046] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]ack server push request,request=NotifySubscriberRequest,requestId=21
00:32:15.714 [nacos-grpc-client-executor-1047] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]receive server push request,request=NotifySubscriberRequest,requestId=22
00:32:15.714 [nacos-grpc-client-executor-1047] INFO  c.a.n.client.naming - [isChangedServiceInfo,235] - new ips(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
00:32:15.714 [nacos-grpc-client-executor-1047] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
00:32:15.715 [nacos-grpc-client-executor-1047] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]ack server push request,request=NotifySubscriberRequest,requestId=22
00:33:49.008 [nacos-grpc-client-executor-1079] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]receive server push request,request=NotifySubscriberRequest,requestId=27
00:33:49.009 [nacos-grpc-client-executor-1079] INFO  c.a.n.client.naming - [isChangedServiceInfo,241] - removed ips(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
00:33:49.009 [nacos-grpc-client-executor-1079] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@logictrue-interfaces -> []
00:33:49.009 [nacos-grpc-client-executor-1079] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]ack server push request,request=NotifySubscriberRequest,requestId=27
00:33:53.174 [nacos-grpc-client-executor-1080] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]receive server push request,request=NotifySubscriberRequest,requestId=28
00:33:53.175 [nacos-grpc-client-executor-1080] INFO  c.a.n.client.naming - [isChangedServiceInfo,235] - new ips(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
00:33:53.175 [nacos-grpc-client-executor-1080] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
00:33:53.175 [nacos-grpc-client-executor-1080] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]ack server push request,request=NotifySubscriberRequest,requestId=28
00:38:17.409 [nacos-grpc-client-executor-1168] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]receive server push request,request=NotifySubscriberRequest,requestId=30
00:38:17.410 [nacos-grpc-client-executor-1168] INFO  c.a.n.client.naming - [isChangedServiceInfo,241] - removed ips(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
00:38:17.410 [nacos-grpc-client-executor-1168] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@logictrue-interfaces -> []
00:38:17.410 [nacos-grpc-client-executor-1168] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]ack server push request,request=NotifySubscriberRequest,requestId=30
00:39:13.311 [nacos-grpc-client-executor-1183] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]receive server push request,request=NotifySubscriberRequest,requestId=31
00:39:13.311 [nacos-grpc-client-executor-1183] INFO  c.a.n.client.naming - [isChangedServiceInfo,235] - new ips(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
00:39:13.311 [nacos-grpc-client-executor-1183] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
00:39:13.311 [nacos-grpc-client-executor-1183] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]ack server push request,request=NotifySubscriberRequest,requestId=31
00:47:06.672 [nacos-grpc-client-executor-1339] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]receive server push request,request=NotifySubscriberRequest,requestId=38
00:47:06.672 [nacos-grpc-client-executor-1339] INFO  c.a.n.client.naming - [isChangedServiceInfo,241] - removed ips(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
00:47:06.672 [nacos-grpc-client-executor-1339] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@logictrue-interfaces -> []
00:47:06.673 [nacos-grpc-client-executor-1339] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]ack server push request,request=NotifySubscriberRequest,requestId=38
00:54:39.140 [nacos-grpc-client-executor-1488] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]receive server push request,request=NotifySubscriberRequest,requestId=43
00:54:39.140 [nacos-grpc-client-executor-1488] INFO  c.a.n.client.naming - [isChangedServiceInfo,235] - new ips(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
00:54:39.141 [nacos-grpc-client-executor-1488] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
00:54:39.141 [nacos-grpc-client-executor-1488] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]ack server push request,request=NotifySubscriberRequest,requestId=43
00:59:21.788 [nacos-grpc-client-executor-1575] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]receive server push request,request=NotifySubscriberRequest,requestId=45
00:59:21.788 [nacos-grpc-client-executor-1575] INFO  c.a.n.client.naming - [isChangedServiceInfo,241] - removed ips(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
00:59:21.789 [nacos-grpc-client-executor-1575] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@logictrue-interfaces -> []
00:59:21.789 [nacos-grpc-client-executor-1575] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]ack server push request,request=NotifySubscriberRequest,requestId=45
01:00:15.681 [nacos-grpc-client-executor-1595] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]receive server push request,request=NotifySubscriberRequest,requestId=46
01:00:15.682 [nacos-grpc-client-executor-1595] INFO  c.a.n.client.naming - [isChangedServiceInfo,235] - new ips(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
01:00:15.682 [nacos-grpc-client-executor-1595] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
01:00:15.682 [nacos-grpc-client-executor-1595] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]ack server push request,request=NotifySubscriberRequest,requestId=46
01:05:20.369 [nacos-grpc-client-executor-1691] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]receive server push request,request=NotifySubscriberRequest,requestId=48
01:05:20.370 [nacos-grpc-client-executor-1691] INFO  c.a.n.client.naming - [isChangedServiceInfo,241] - removed ips(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
01:05:20.370 [nacos-grpc-client-executor-1691] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@logictrue-interfaces -> []
01:05:20.372 [nacos-grpc-client-executor-1691] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]ack server push request,request=NotifySubscriberRequest,requestId=48
01:06:12.624 [nacos-grpc-client-executor-1712] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]receive server push request,request=NotifySubscriberRequest,requestId=49
01:06:12.624 [nacos-grpc-client-executor-1712] INFO  c.a.n.client.naming - [isChangedServiceInfo,235] - new ips(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
01:06:12.624 [nacos-grpc-client-executor-1712] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
01:06:12.624 [nacos-grpc-client-executor-1712] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]ack server push request,request=NotifySubscriberRequest,requestId=49
01:23:48.485 [nacos-grpc-client-executor-2041] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]receive server push request,request=NotifySubscriberRequest,requestId=51
01:23:48.486 [nacos-grpc-client-executor-2041] INFO  c.a.n.client.naming - [isChangedServiceInfo,241] - removed ips(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
01:23:48.486 [nacos-grpc-client-executor-2041] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@logictrue-interfaces -> []
01:23:48.486 [nacos-grpc-client-executor-2041] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]ack server push request,request=NotifySubscriberRequest,requestId=51
01:24:41.160 [nacos-grpc-client-executor-2060] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]receive server push request,request=NotifySubscriberRequest,requestId=52
01:24:41.160 [nacos-grpc-client-executor-2060] INFO  c.a.n.client.naming - [isChangedServiceInfo,235] - new ips(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
01:24:41.160 [nacos-grpc-client-executor-2060] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
01:24:41.161 [nacos-grpc-client-executor-2060] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]ack server push request,request=NotifySubscriberRequest,requestId=52
01:55:42.818 [reactor-http-epoll-8] INFO  c.l.g.f.XssFilter - [filter,68] - Xss过滤器执行成功 地址：http://127.0.0.1:8080/auth/login 消耗时间：0
01:55:53.392 [reactor-http-epoll-10] INFO  c.l.g.f.XssFilter - [filter,68] - Xss过滤器执行成功 地址：http://127.0.0.1:8080/auth/login 消耗时间：0
09:21:10.429 [nacos-grpc-client-executor-2809] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]receive server push request,request=ClientDetectionRequest,requestId=55
09:21:10.433 [nacos-grpc-client-executor-2809] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a37a5bf9-9fd3-449c-9510-5a69e0b34093]ack server push request,request=ClientDetectionRequest,requestId=55
09:21:10.457 [nacos-grpc-client-executor-1762] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46502396-1920-4db5-b6f0-2b64c9c35cf7_config-0]receive server push request,request=ClientDetectionRequest,requestId=58
09:21:10.457 [nacos-grpc-client-executor-1753] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f92e5218-4035-4220-a8b5-e8d7c88a7c91_config-0]receive server push request,request=ClientDetectionRequest,requestId=59
09:21:10.458 [nacos-grpc-client-executor-1753] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f92e5218-4035-4220-a8b5-e8d7c88a7c91_config-0]ack server push request,request=ClientDetectionRequest,requestId=59
09:21:10.458 [nacos-grpc-client-executor-1762] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46502396-1920-4db5-b6f0-2b64c9c35cf7_config-0]ack server push request,request=ClientDetectionRequest,requestId=58
10:15:40.228 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.0.Final
10:15:41.654 [main] INFO  c.a.n.c.c.i.LocalConfigInfoProcessor - [<clinit>,67] - LOCAL_SNAPSHOT_PATH:/home/<USER>/nacos/config
10:15:41.657 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,77] - [RpcClientFactory] create a new rpc client of 4cea2a4c-5c81-4d1a-97a3-b76f58c2e616_config-0
10:15:41.706 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 27 ms to scan 1 urls, producing 3 keys and 6 values 
10:15:41.733 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
10:15:41.743 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
10:15:41.877 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 132 ms to scan 247 urls, producing 0 keys and 0 values 
10:15:41.887 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
10:15:41.897 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
10:15:41.910 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
10:15:42.062 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 148 ms to scan 247 urls, producing 0 keys and 0 values 
10:15:42.064 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4cea2a4c-5c81-4d1a-97a3-b76f58c2e616_config-0]RpcClient init label, labels={module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:15:42.065 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4cea2a4c-5c81-4d1a-97a3-b76f58c2e616_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$332/1717739363
10:15:42.065 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4cea2a4c-5c81-4d1a-97a3-b76f58c2e616_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$333/138536309
10:15:42.066 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4cea2a4c-5c81-4d1a-97a3-b76f58c2e616_config-0]Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:15:42.067 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4cea2a4c-5c81-4d1a-97a3-b76f58c2e616_config-0]RpcClient init, ServerListFactory =com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:15:42.078 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4cea2a4c-5c81-4d1a-97a3-b76f58c2e616_config-0] Try to connect to server on start up, server: {serverIp='127.0.0.1', server main port=8848}
10:15:42.581 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4cea2a4c-5c81-4d1a-97a3-b76f58c2e616_config-0] Success to connect to server [127.0.0.1:8848] on start up,connectionId=1755915342390_172.21.0.1_46830
10:15:42.582 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4cea2a4c-5c81-4d1a-97a3-b76f58c2e616_config-0]Notify connected event to listeners.
10:15:42.582 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4cea2a4c-5c81-4d1a-97a3-b76f58c2e616_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:15:42.582 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,681] - [4cea2a4c-5c81-4d1a-97a3-b76f58c2e616_config-0] Connected,notify listen context...
10:15:42.583 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4cea2a4c-5c81-4d1a-97a3-b76f58c2e616_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$4
10:15:42.612 [main] INFO  c.a.n.c.c.i.Limiter - [<clinit>,56] - limitTime:5.0
10:15:42.625 [main] INFO  c.a.n.c.c.u.JvmUtil - [<clinit>,53] - isMultiInstance:false
10:15:42.682 [main] INFO  c.l.g.LogicTrueGatewayApplication - [logStartupProfileInfo,663] - The following profiles are active: dev
10:15:49.179 [main] INFO  org.redisson.Version - [logVersion,41] - Redisson 3.17.5
10:15:49.418 [redisson-netty-5-5] INFO  o.r.c.p.MasterPubSubConnectionPool - [lambda$createConnection$1,158] - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
10:15:49.525 [redisson-netty-5-18] INFO  o.r.c.p.MasterConnectionPool - [lambda$createConnection$1,158] - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
10:15:50.803 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
10:15:50.983 [main] INFO  c.a.n.client.naming - [initNamespaceForNaming,63] - initializer namespace from System Property : null
10:15:50.984 [main] INFO  c.a.n.client.naming - [call,69] - initializer namespace from System Environment :null
10:15:50.984 [main] INFO  c.a.n.client.naming - [call,79] - initializer namespace from System Property :null
10:15:50.994 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,77] - [RpcClientFactory] create a new rpc client of 1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c
10:15:50.994 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]RpcClient init label, labels={module=naming, source=sdk}
10:15:50.996 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]RpcClient init, ServerListFactory =com.alibaba.nacos.client.naming.core.ServerListManager
10:15:50.996 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:15:50.996 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:15:50.997 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c] Try to connect to server on start up, server: {serverIp='127.0.0.1', server main port=8848}
10:15:51.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c] Success to connect to server [127.0.0.1:8848] on start up,connectionId=1755915351001_172.21.0.1_44708
10:15:51.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:15:51.107 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]Notify connected event to listeners.
10:15:51.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$4
10:15:51.107 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.client.naming - [onConnected,76] - Grpc connection connect
10:15:51.292 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
10:15:51.569 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] sentinel-logictrue-gateway+DEFAULT_GROUP
10:15:51.574 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=, dataId=sentinel-logictrue-gateway, group=DEFAULT_GROUP, cnt=1
10:15:51.575 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,77] - [RpcClientFactory] create a new rpc client of 95d7ec77-089e-4abb-89f0-df04111a8279_config-0
10:15:51.576 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95d7ec77-089e-4abb-89f0-df04111a8279_config-0]RpcClient init label, labels={module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:15:51.576 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95d7ec77-089e-4abb-89f0-df04111a8279_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$332/1717739363
10:15:51.576 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95d7ec77-089e-4abb-89f0-df04111a8279_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$333/138536309
10:15:51.577 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95d7ec77-089e-4abb-89f0-df04111a8279_config-0]Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:15:51.577 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95d7ec77-089e-4abb-89f0-df04111a8279_config-0]RpcClient init, ServerListFactory =com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:15:51.578 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95d7ec77-089e-4abb-89f0-df04111a8279_config-0] Try to connect to server on start up, server: {serverIp='127.0.0.1', server main port=8848}
10:15:51.692 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95d7ec77-089e-4abb-89f0-df04111a8279_config-0] Success to connect to server [127.0.0.1:8848] on start up,connectionId=1755915351584_172.21.0.1_44720
10:15:51.693 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95d7ec77-089e-4abb-89f0-df04111a8279_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:15:51.693 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95d7ec77-089e-4abb-89f0-df04111a8279_config-0]Notify connected event to listeners.
10:15:51.693 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95d7ec77-089e-4abb-89f0-df04111a8279_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$4
10:15:51.693 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.c.i.ClientWorker - [onConnected,681] - [95d7ec77-089e-4abb-89f0-df04111a8279_config-0] Connected,notify listen context...
10:15:51.774 [main] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@logictrue-gateway@@DEFAULT -> []
10:15:51.776 [main] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@logictrue-gateway@@DEFAULT -> []
10:15:51.853 [boundedElastic-3] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(1) service: DEFAULT_GROUP@@logictrue-auth -> [{"instanceId":"**************#9211#DEFAULT#DEFAULT_GROUP@@logictrue-auth","ip":"**************","port":9211,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-auth","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
10:15:51.853 [boundedElastic-4] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(1) service: DEFAULT_GROUP@@logictrue-auth -> [{"instanceId":"**************#9211#DEFAULT#DEFAULT_GROUP@@logictrue-auth","ip":"**************","port":9211,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-auth","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
10:15:51.853 [boundedElastic-3] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-auth -> [{"instanceId":"**************#9211#DEFAULT#DEFAULT_GROUP@@logictrue-auth","ip":"**************","port":9211,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-auth","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
10:15:51.853 [boundedElastic-4] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-auth -> [{"instanceId":"**************#9211#DEFAULT#DEFAULT_GROUP@@logictrue-auth","ip":"**************","port":9211,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-auth","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
10:15:52.337 [nacos-grpc-client-executor-16] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]receive server push request,request=NotifySubscriberRequest,requestId=2
10:15:52.337 [nacos-grpc-client-executor-16] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]ack server push request,request=NotifySubscriberRequest,requestId=2
10:15:52.421 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]receive server push request,request=NotifySubscriberRequest,requestId=3
10:15:52.422 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]ack server push request,request=NotifySubscriberRequest,requestId=3
10:15:53.818 [main] INFO  c.a.n.client.naming - [registerService,112] - [REGISTER-SERVICE] dev registering service logictrue-gateway with instance Instance{instanceId='null', ip='**************', port=8080, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
10:15:53.837 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP logictrue-gateway **************:8080 register finished
10:15:53.897 [boundedElastic-2] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(1) service: DEFAULT_GROUP@@logictrue-gateway -> [{"instanceId":"**************#8080#DEFAULT#DEFAULT_GROUP@@logictrue-gateway","ip":"**************","port":8080,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-gateway","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
10:15:53.897 [boundedElastic-2] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-gateway -> [{"instanceId":"**************#8080#DEFAULT#DEFAULT_GROUP@@logictrue-gateway","ip":"**************","port":8080,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-gateway","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
10:15:53.905 [main] INFO  c.l.g.LogicTrueGatewayApplication - [logStarted,61] - Started LogicTrueGatewayApplication in 14.038 seconds (JVM running for 14.47)
10:15:53.920 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] logictrue-gateway+DEFAULT_GROUP+dev
10:15:53.921 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=dev, dataId=logictrue-gateway, group=DEFAULT_GROUP, cnt=1
10:15:53.922 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] logictrue-gateway-dev.yml+DEFAULT_GROUP+dev
10:15:53.923 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=dev, dataId=logictrue-gateway-dev.yml, group=DEFAULT_GROUP, cnt=1
10:15:53.932 [main] INFO  c.a.n.c.c.i.ClientWorker - [addCacheDataIfAbsent,384] - [config_rpc_client] [subscribe] logictrue-gateway.yml+DEFAULT_GROUP+dev
10:15:53.932 [main] INFO  c.a.n.c.c.i.CacheData - [addListener,173] - [config_rpc_client] [add-listener] ok, tenant=dev, dataId=logictrue-gateway.yml, group=DEFAULT_GROUP, cnt=1
10:15:54.440 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]receive server push request,request=NotifySubscriberRequest,requestId=4
10:15:54.441 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]ack server push request,request=NotifySubscriberRequest,requestId=4
10:15:54.782 [com.alibaba.nacos.client.naming.updater.2] INFO  c.a.n.client.naming - [isChangedServiceInfo,235] - new ips(1) service: DEFAULT_GROUP@@logictrue-gateway@@DEFAULT -> [{"instanceId":"**************#8080#DEFAULT#DEFAULT_GROUP@@logictrue-gateway","ip":"**************","port":8080,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-gateway","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
10:15:54.785 [com.alibaba.nacos.client.naming.updater.2] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-gateway@@DEFAULT -> [{"instanceId":"**************#8080#DEFAULT#DEFAULT_GROUP@@logictrue-gateway","ip":"**************","port":8080,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-gateway","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
10:16:23.039 [boundedElastic-4] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(1) service: DEFAULT_GROUP@@logictrue-system -> [{"instanceId":"**************#9201#DEFAULT#DEFAULT_GROUP@@logictrue-system","ip":"**************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
10:16:23.040 [boundedElastic-4] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-system -> [{"instanceId":"**************#9201#DEFAULT#DEFAULT_GROUP@@logictrue-system","ip":"**************","port":9201,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-system","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
10:16:23.609 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]receive server push request,request=NotifySubscriberRequest,requestId=6
10:16:23.610 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]ack server push request,request=NotifySubscriberRequest,requestId=6
10:16:54.129 [boundedElastic-3] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
10:16:54.129 [boundedElastic-3] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
10:16:54.637 [nacos-grpc-client-executor-60] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]receive server push request,request=NotifySubscriberRequest,requestId=9
10:16:54.638 [nacos-grpc-client-executor-60] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]ack server push request,request=NotifySubscriberRequest,requestId=9
10:21:30.970 [boundedElastic-3] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@logictrue-llm -> []
10:21:30.971 [boundedElastic-3] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@logictrue-llm -> []
10:21:31.482 [nacos-grpc-client-executor-164] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]receive server push request,request=NotifySubscriberRequest,requestId=10
10:21:31.483 [nacos-grpc-client-executor-164] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]ack server push request,request=NotifySubscriberRequest,requestId=10
10:21:31.496 [boundedElastic-5] INFO  c.a.n.client.naming - [isChangedServiceInfo,181] - init new ips(0) service: DEFAULT_GROUP@@logictrue-file -> []
10:21:31.497 [boundedElastic-5] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@logictrue-file -> []
10:21:32.083 [nacos-grpc-client-executor-170] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]receive server push request,request=NotifySubscriberRequest,requestId=11
10:21:32.083 [nacos-grpc-client-executor-170] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]ack server push request,request=NotifySubscriberRequest,requestId=11
10:25:10.122 [nacos-grpc-client-executor-265] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]receive server push request,request=NotifySubscriberRequest,requestId=13
10:25:10.123 [nacos-grpc-client-executor-265] INFO  c.a.n.client.naming - [isChangedServiceInfo,241] - removed ips(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
10:25:10.124 [nacos-grpc-client-executor-265] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@logictrue-interfaces -> []
10:25:10.124 [nacos-grpc-client-executor-265] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]ack server push request,request=NotifySubscriberRequest,requestId=13
10:26:05.095 [nacos-grpc-client-executor-293] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]receive server push request,request=NotifySubscriberRequest,requestId=15
10:26:05.095 [nacos-grpc-client-executor-293] INFO  c.a.n.client.naming - [isChangedServiceInfo,235] - new ips(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
10:26:05.095 [nacos-grpc-client-executor-293] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
10:26:05.096 [nacos-grpc-client-executor-293] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]ack server push request,request=NotifySubscriberRequest,requestId=15
10:31:10.944 [nacos-grpc-client-executor-413] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]receive server push request,request=NotifySubscriberRequest,requestId=17
10:31:10.945 [nacos-grpc-client-executor-413] INFO  c.a.n.client.naming - [isChangedServiceInfo,241] - removed ips(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
10:31:10.945 [nacos-grpc-client-executor-413] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(0) service: DEFAULT_GROUP@@logictrue-interfaces -> []
10:31:10.945 [nacos-grpc-client-executor-413] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]ack server push request,request=NotifySubscriberRequest,requestId=17
10:32:11.363 [nacos-grpc-client-executor-436] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]receive server push request,request=NotifySubscriberRequest,requestId=20
10:32:11.363 [nacos-grpc-client-executor-436] INFO  c.a.n.client.naming - [isChangedServiceInfo,235] - new ips(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
10:32:11.364 [nacos-grpc-client-executor-436] INFO  c.a.n.client.naming - [processServiceInfo,166] - current ips:(1) service: DEFAULT_GROUP@@logictrue-interfaces -> [{"instanceId":"**************#9210#DEFAULT#DEFAULT_GROUP@@logictrue-interfaces","ip":"**************","port":9210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@logictrue-interfaces","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
10:32:11.364 [nacos-grpc-client-executor-436] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a94c17b-8a4e-4b52-8638-4f1bf2bedb2c]ack server push request,request=NotifySubscriberRequest,requestId=20
